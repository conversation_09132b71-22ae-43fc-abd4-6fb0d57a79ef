FROM gradle:8.14.2-jdk21 AS build

WORKDIR /app

# Docker cache deps
COPY gradle.properties /app/
COPY gradle/ /app/gradle/
COPY gradlew /app/
COPY settings.gradle.kts /app/
COPY build.gradle.kts /app/
COPY common/build.gradle.kts /app/common/build.gradle.kts
COPY protos/build.gradle.kts /app/protos/build.gradle.kts
COPY server/build.gradle.kts /app/server/build.gradle.kts

RUN gradle wrapper --no-daemon && \
    ./gradlew dependencies --no-daemon

COPY common/src /app/common/src
COPY protos/src /app/protos/src
COPY server/src /app/server/src

RUN ./gradlew :server:distTar --no-daemon && \
    cd server/build/distributions && \
    tar xf sdk.tar

FROM eclipse-temurin:21.0.7_6-jre

COPY --from=build /app/server/build/distributions/sdk /opt/sdk/

USER 1000

CMD ["/bin/bash", "/opt/sdk/bin/server"]
