package app.closer

import android.app.Application
import app.closer.di.initKoin
import app.closer.sdk.Closer
import app.closer.sdk.Config
import org.koin.android.ext.koin.androidContext
import org.koin.android.ext.koin.androidLogger

class MyApplication : Application() {

    private val config = object : Config {
        override fun context(): Any = applicationContext
        override fun address(): String = "sdk.closerbot.lab.lekta.ai"
        override fun port(): Int = 443
    }

    override fun onCreate() {
        super.onCreate()
        Closer.initialize(config)

        initKoin(
            applicationContext = applicationContext
        ) {
            androidLogger()
            androidContext(this@MyApplication)
        }
    }
}
