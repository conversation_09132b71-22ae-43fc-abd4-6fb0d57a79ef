package app.closer.config.data.storage

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.preferencesDataStore

private val Context.appDataStore: DataStore<Preferences> by preferencesDataStore(name = "app_config_settings")

actual fun createAppDataStore(context: Any): DataStore<Preferences> {
    val androidContext = context as? Context
        ?: throw IllegalArgumentException("Android context provider did not return a Context")
    
    return androidContext.applicationContext.appDataStore
}
