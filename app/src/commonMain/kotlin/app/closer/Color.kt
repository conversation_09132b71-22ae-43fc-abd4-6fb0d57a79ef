package app.closer
import androidx.compose.ui.graphics.Color

val primaryLight = Color(0xFF6D5E0F)
val onPrimaryLight = Color(0xFFFFFFFF)
val primaryContainerLight = Color(0xFFF8E287)
val onPrimaryContainerLight = Color(0xFF534600)
val secondaryLight = Color(0xFF665E40)
val onSecondaryLight = Color(0xFFFFFFFF)
val secondaryContainerLight = Color(0xFFEEE2BC)
val onSecondaryContainerLight = Color(0xFF4E472A)
val tertiaryLight = Color(0xFF43664E)
val onTertiaryLight = Color(0xFFFFFFFF)
val tertiaryContainerLight = Color(0xFFC5ECCE)
val onTertiaryContainerLight = Color(0xFF2C4E38)
val errorLight = Color(0xFFBA1A1A)
val onErrorLight = Color(0xFFFFFFFF)
val errorContainerLight = Color(0xFFFFDAD6)
val onErrorContainerLight = Color(0xFF93000A)
val backgroundLight = Color(0xFFFFF9EE)
val onBackgroundLight = Color(0xFF1E1B13)
val surfaceLight = Color(0xFFFFF9EE)
val onSurfaceLight = Color(0xFF1E1B13)
val surfaceVariantLight = Color(0xFFEAE2D0)
val onSurfaceVariantLight = Color(0xFF4B4739)
val outlineLight = Color(0xFF7C7767)
val outlineVariantLight = Color(0xFFCDC6B4)
val scrimLight = Color(0xFF000000)
val inverseSurfaceLight = Color(0xFF333027)
val inverseOnSurfaceLight = Color(0xFFF7F0E2)
val inversePrimaryLight = Color(0xFFDBC66E)
val surfaceDimLight = Color(0xFFE0D9CC)
val surfaceBrightLight = Color(0xFFFFF9EE)
val surfaceContainerLowestLight = Color(0xFFFFFFFF)
val surfaceContainerLowLight = Color(0xFFFAF3E5)
val surfaceContainerLight = Color(0xFFF4EDDF)
val surfaceContainerHighLight = Color(0xFFEEE8DA)
val surfaceContainerHighestLight = Color(0xFFE8E2D4)

val primaryLightMediumContrast = Color(0xFF403600)
val onPrimaryLightMediumContrast = Color(0xFFFFFFFF)
val primaryContainerLightMediumContrast = Color(0xFF7D6C1E)
val onPrimaryContainerLightMediumContrast = Color(0xFFFFFFFF)
val secondaryLightMediumContrast = Color(0xFF3C361B)
val onSecondaryLightMediumContrast = Color(0xFFFFFFFF)
val secondaryContainerLightMediumContrast = Color(0xFF756D4E)
val onSecondaryContainerLightMediumContrast = Color(0xFFFFFFFF)
val tertiaryLightMediumContrast = Color(0xFF1B3D28)
val onTertiaryLightMediumContrast = Color(0xFFFFFFFF)
val tertiaryContainerLightMediumContrast = Color(0xFF52755D)
val onTertiaryContainerLightMediumContrast = Color(0xFFFFFFFF)
val errorLightMediumContrast = Color(0xFF740006)
val onErrorLightMediumContrast = Color(0xFFFFFFFF)
val errorContainerLightMediumContrast = Color(0xFFCF2C27)
val onErrorContainerLightMediumContrast = Color(0xFFFFFFFF)
val backgroundLightMediumContrast = Color(0xFFFFF9EE)
val onBackgroundLightMediumContrast = Color(0xFF1E1B13)
val surfaceLightMediumContrast = Color(0xFFFFF9EE)
val onSurfaceLightMediumContrast = Color(0xFF131109)
val surfaceVariantLightMediumContrast = Color(0xFFEAE2D0)
val onSurfaceVariantLightMediumContrast = Color(0xFF3A3629)
val outlineLightMediumContrast = Color(0xFF575244)
val outlineVariantLightMediumContrast = Color(0xFF726D5E)
val scrimLightMediumContrast = Color(0xFF000000)
val inverseSurfaceLightMediumContrast = Color(0xFF333027)
val inverseOnSurfaceLightMediumContrast = Color(0xFFF7F0E2)
val inversePrimaryLightMediumContrast = Color(0xFFDBC66E)
val surfaceDimLightMediumContrast = Color(0xFFCCC6B9)
val surfaceBrightLightMediumContrast = Color(0xFFFFF9EE)
val surfaceContainerLowestLightMediumContrast = Color(0xFFFFFFFF)
val surfaceContainerLowLightMediumContrast = Color(0xFFFAF3E5)
val surfaceContainerLightMediumContrast = Color(0xFFEEE8DA)
val surfaceContainerHighLightMediumContrast = Color(0xFFE3DCCF)
val surfaceContainerHighestLightMediumContrast = Color(0xFFD7D1C4)

val primaryLightHighContrast = Color(0xFF352C00)
val onPrimaryLightHighContrast = Color(0xFFFFFFFF)
val primaryContainerLightHighContrast = Color(0xFF564800)
val onPrimaryContainerLightHighContrast = Color(0xFFFFFFFF)
val secondaryLightHighContrast = Color(0xFF322C12)
val onSecondaryLightHighContrast = Color(0xFFFFFFFF)
val secondaryContainerLightHighContrast = Color(0xFF50492D)
val onSecondaryContainerLightHighContrast = Color(0xFFFFFFFF)
val tertiaryLightHighContrast = Color(0xFF10321F)
val onTertiaryLightHighContrast = Color(0xFFFFFFFF)
val tertiaryContainerLightHighContrast = Color(0xFF2E503A)
val onTertiaryContainerLightHighContrast = Color(0xFFFFFFFF)
val errorLightHighContrast = Color(0xFF600004)
val onErrorLightHighContrast = Color(0xFFFFFFFF)
val errorContainerLightHighContrast = Color(0xFF98000A)
val onErrorContainerLightHighContrast = Color(0xFFFFFFFF)
val backgroundLightHighContrast = Color(0xFFFFF9EE)
val onBackgroundLightHighContrast = Color(0xFF1E1B13)
val surfaceLightHighContrast = Color(0xFFFFF9EE)
val onSurfaceLightHighContrast = Color(0xFF000000)
val surfaceVariantLightHighContrast = Color(0xFFEAE2D0)
val onSurfaceVariantLightHighContrast = Color(0xFF000000)
val outlineLightHighContrast = Color(0xFF2F2C20)
val outlineVariantLightHighContrast = Color(0xFF4D493B)
val scrimLightHighContrast = Color(0xFF000000)
val inverseSurfaceLightHighContrast = Color(0xFF333027)
val inverseOnSurfaceLightHighContrast = Color(0xFFFFFFFF)
val inversePrimaryLightHighContrast = Color(0xFFDBC66E)
val surfaceDimLightHighContrast = Color(0xFFBEB8AB)
val surfaceBrightLightHighContrast = Color(0xFFFFF9EE)
val surfaceContainerLowestLightHighContrast = Color(0xFFFFFFFF)
val surfaceContainerLowLightHighContrast = Color(0xFFF7F0E2)
val surfaceContainerLightHighContrast = Color(0xFFE8E2D4)
val surfaceContainerHighLightHighContrast = Color(0xFFDAD4C6)
val surfaceContainerHighestLightHighContrast = Color(0xFFCCC6B9)

val primaryDark = Color(0xFFDBC66E)
val onPrimaryDark = Color(0xFF3A3000)
val primaryContainerDark = Color(0xFF534600)
val onPrimaryContainerDark = Color(0xFFF8E287)
val secondaryDark = Color(0xFFD1C6A1)
val onSecondaryDark = Color(0xFF363016)
val secondaryContainerDark = Color(0xFF4E472A)
val onSecondaryContainerDark = Color(0xFFEEE2BC)
val tertiaryDark = Color(0xFFA9D0B3)
val onTertiaryDark = Color(0xFF143723)
val tertiaryContainerDark = Color(0xFF2C4E38)
val onTertiaryContainerDark = Color(0xFFC5ECCE)
val errorDark = Color(0xFFFFB4AB)
val onErrorDark = Color(0xFF690005)
val errorContainerDark = Color(0xFF93000A)
val onErrorContainerDark = Color(0xFFFFDAD6)
val backgroundDark = Color(0xFF15130B)
val onBackgroundDark = Color(0xFFE8E2D4)
val surfaceDark = Color(0xFF15130B)
val onSurfaceDark = Color(0xFFE8E2D4)
val surfaceVariantDark = Color(0xFF4B4739)
val onSurfaceVariantDark = Color(0xFFCDC6B4)
val outlineDark = Color(0xFF969080)
val outlineVariantDark = Color(0xFF4B4739)
val scrimDark = Color(0xFF000000)
val inverseSurfaceDark = Color(0xFFE8E2D4)
val inverseOnSurfaceDark = Color(0xFF333027)
val inversePrimaryDark = Color(0xFF6D5E0F)
val surfaceDimDark = Color(0xFF15130B)
val surfaceBrightDark = Color(0xFF3C3930)
val surfaceContainerLowestDark = Color(0xFF100E07)
val surfaceContainerLowDark = Color(0xFF1E1B13)
val surfaceContainerDark = Color(0xFF222017)
val surfaceContainerHighDark = Color(0xFF2D2A21)
val surfaceContainerHighestDark = Color(0xFF38352B)

val primaryDarkMediumContrast = Color(0xFFF2DC82)
val onPrimaryDarkMediumContrast = Color(0xFF2D2500)
val primaryContainerDarkMediumContrast = Color(0xFFA3903F)
val onPrimaryContainerDarkMediumContrast = Color(0xFF000000)
val secondaryDarkMediumContrast = Color(0xFFE8DCB6)
val onSecondaryDarkMediumContrast = Color(0xFF2B250C)
val secondaryContainerDarkMediumContrast = Color(0xFF9A916F)
val onSecondaryContainerDarkMediumContrast = Color(0xFF000000)
val tertiaryDarkMediumContrast = Color(0xFFBFE6C8)
val onTertiaryDarkMediumContrast = Color(0xFF082C18)
val tertiaryContainerDarkMediumContrast = Color(0xFF75997F)
val onTertiaryContainerDarkMediumContrast = Color(0xFF000000)
val errorDarkMediumContrast = Color(0xFFFFD2CC)
val onErrorDarkMediumContrast = Color(0xFF540003)
val errorContainerDarkMediumContrast = Color(0xFFFF5449)
val onErrorContainerDarkMediumContrast = Color(0xFF000000)
val backgroundDarkMediumContrast = Color(0xFF15130B)
val onBackgroundDarkMediumContrast = Color(0xFFE8E2D4)
val surfaceDarkMediumContrast = Color(0xFF15130B)
val onSurfaceDarkMediumContrast = Color(0xFFFFFFFF)
val surfaceVariantDarkMediumContrast = Color(0xFF4B4739)
val onSurfaceVariantDarkMediumContrast = Color(0xFFE3DCCA)
val outlineDarkMediumContrast = Color(0xFFB8B1A0)
val outlineVariantDarkMediumContrast = Color(0xFF969080)
val scrimDarkMediumContrast = Color(0xFF000000)
val inverseSurfaceDarkMediumContrast = Color(0xFFE8E2D4)
val inverseOnSurfaceDarkMediumContrast = Color(0xFF2D2A21)
val inversePrimaryDarkMediumContrast = Color(0xFF554700)
val surfaceDimDarkMediumContrast = Color(0xFF15130B)
val surfaceBrightDarkMediumContrast = Color(0xFF48443A)
val surfaceContainerLowestDarkMediumContrast = Color(0xFF090703)
val surfaceContainerLowDarkMediumContrast = Color(0xFF201D15)
val surfaceContainerDarkMediumContrast = Color(0xFF2A281F)
val surfaceContainerHighDarkMediumContrast = Color(0xFF353229)
val surfaceContainerHighestDarkMediumContrast = Color(0xFF413D34)

val primaryDarkHighContrast = Color(0xFFFFF0BA)
val onPrimaryDarkHighContrast = Color(0xFF000000)
val primaryContainerDarkHighContrast = Color(0xFFD7C26B)
val onPrimaryContainerDarkHighContrast = Color(0xFF0F0B00)
val secondaryDarkHighContrast = Color(0xFFFCF0C9)
val onSecondaryDarkHighContrast = Color(0xFF000000)
val secondaryContainerDarkHighContrast = Color(0xFFCDC29E)
val onSecondaryContainerDarkHighContrast = Color(0xFF0F0B00)
val tertiaryDarkHighContrast = Color(0xFFD2FADB)
val onTertiaryDarkHighContrast = Color(0xFF000000)
val tertiaryContainerDarkHighContrast = Color(0xFFA5CCAF)
val onTertiaryContainerDarkHighContrast = Color(0xFF000F05)
val errorDarkHighContrast = Color(0xFFFFECE9)
val onErrorDarkHighContrast = Color(0xFF000000)
val errorContainerDarkHighContrast = Color(0xFFFFAEA4)
val onErrorContainerDarkHighContrast = Color(0xFF220001)
val backgroundDarkHighContrast = Color(0xFF15130B)
val onBackgroundDarkHighContrast = Color(0xFFE8E2D4)
val surfaceDarkHighContrast = Color(0xFF15130B)
val onSurfaceDarkHighContrast = Color(0xFFFFFFFF)
val surfaceVariantDarkHighContrast = Color(0xFF4B4739)
val onSurfaceVariantDarkHighContrast = Color(0xFFFFFFFF)
val outlineDarkHighContrast = Color(0xFFF7EFDD)
val outlineVariantDarkHighContrast = Color(0xFFC9C2B1)
val scrimDarkHighContrast = Color(0xFF000000)
val inverseSurfaceDarkHighContrast = Color(0xFFE8E2D4)
val inverseOnSurfaceDarkHighContrast = Color(0xFF000000)
val inversePrimaryDarkHighContrast = Color(0xFF554700)
val surfaceDimDarkHighContrast = Color(0xFF15130B)
val surfaceBrightDarkHighContrast = Color(0xFF535046)
val surfaceContainerLowestDarkHighContrast = Color(0xFF000000)
val surfaceContainerLowDarkHighContrast = Color(0xFF222017)
val surfaceContainerDarkHighContrast = Color(0xFF333027)
val surfaceContainerHighDarkHighContrast = Color(0xFF3E3B32)
val surfaceContainerHighestDarkHighContrast = Color(0xFF4A473D)







