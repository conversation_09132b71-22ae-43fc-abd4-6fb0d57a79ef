package app.closer.chat

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import org.koin.compose.viewmodel.koinViewModel
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import app.closer.sdk.messaging.RTCService.SubscribeState

@Composable
fun ConnectionStatusIndicator(state: SubscribeState) {
    val color = when (state) {
        SubscribeState.Subscribed -> Color(0xFF4CAF50)
        SubscribeState.Subscribing -> Color(0xFFFFC107)
        SubscribeState.Unsubscribed -> Color(0xFF9E9E9E)
        SubscribeState.Failed -> Color(0xFFF44336)
    }

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(bottom = 8.dp),
        horizontalArrangement = Arrangement.Start,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = state.name,
            color = MaterialTheme.colorScheme.onSurface
        )
        Box(
            modifier = Modifier
                .padding(4.dp)
                .size(12.dp)
                .background(color = color, shape = RoundedCornerShape(50))
        )
    }
}


@Composable
fun ChatScreen(viewModel: ChatViewModel = koinViewModel<ChatViewModel>()) {

    val colorScheme = MaterialTheme.colorScheme
    val userBubbleColor = colorScheme.primary
    val agentBubbleColor = colorScheme.secondary
    val userTextColor = colorScheme.onPrimary
    val agentTextColor = colorScheme.onSecondary

    val cornerRadius = 16.dp

    val messages by viewModel.messages.collectAsState()
    val connectionState by viewModel.subscribeState.collectAsState()
    val currentRoomId by viewModel.currentRoomId.collectAsState()
    var input by remember { mutableStateOf("") }

    val listState = rememberLazyListState()

    LaunchedEffect(messages.size) {
        if (messages.isNotEmpty()) {
            listState.animateScrollToItem(messages.lastIndex)
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(colorScheme.surface)
            .padding(16.dp)
            .statusBarsPadding()
    ) {
        ConnectionStatusIndicator(connectionState)

        Text(
            text = if (currentRoomId != null) "Room ID: $currentRoomId" else "No active room",
            style = MaterialTheme.typography.bodySmall
        )
        Spacer(modifier = Modifier.height(16.dp))

        LazyColumn(
            modifier = Modifier.weight(1f),
            state = listState
        ) {
            items(messages.size) { i ->
                val msg = messages[i]
                val bg = if (msg.isUser) userBubbleColor else agentBubbleColor
                val txtColor = if (msg.isUser) userTextColor else agentTextColor
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 4.dp),
                    horizontalArrangement = if (msg.isUser) Arrangement.End else Arrangement.Start
                ) {
                    Surface(
                        modifier = Modifier.padding(4.dp),
                        color = bg,
                        shape = RoundedCornerShape(cornerRadius)
                    ) {
                        Text(
                            modifier = Modifier.padding(8.dp),
                            color = txtColor,
                            text = msg.text
                        )
                    }
                }
            }
        }

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            BasicTextField(
                value = input,
                onValueChange = { input = it },
                modifier = Modifier
                    .weight(1f)
                    .border(
                        width = 1.dp,
                        color = userBubbleColor,
                        shape = RoundedCornerShape(cornerRadius)
                    )
                    .background(
                        color = colorScheme.surface,
                        shape = RoundedCornerShape(cornerRadius)
                    )
                    .padding(horizontal = 12.dp, vertical = 8.dp),
                singleLine = false,
                textStyle = TextStyle(
                    color = MaterialTheme.colorScheme.onSurface,
                    fontSize = 16.sp
                ),
                cursorBrush = SolidColor(MaterialTheme.colorScheme.primary)
            )

            Button(
                onClick = {
                    if (input.isNotBlank()) {
                        viewModel.send(input)
                        input = ""
                    }
                },
                shape = RoundedCornerShape(cornerRadius),
                colors = ButtonDefaults.buttonColors(
                    contentColor = userTextColor,
                    containerColor = userBubbleColor,
                )
            ) {
                Text("Send")
            }
        }
    }
}
