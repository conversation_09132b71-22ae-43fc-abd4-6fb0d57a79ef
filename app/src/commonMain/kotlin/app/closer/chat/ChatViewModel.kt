package app.closer.chat

import app.closer.ChatStreamRequest
import app.closer.ChatStreamResponse
import app.closer.RoomSendMessage
import app.closer.sdk.Closer
import app.closer.sdk.adviserIdFlow
import app.closer.sdk.messaging.RTCService
import app.closer.sdk.messaging.RTCService.SubscribeState
import app.closer.sdk.saveDeviceId
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

data class Message(val text: String, val isUser: Boolean)

class ChatViewModel : ViewModel() {
    private val rtcService: RTCService = Closer.getRTCService()
    private val dataStore = Closer.getDataStore()

    private val _messages = MutableStateFlow<List<Message>>(emptyList())
    val messages: StateFlow<List<Message>> = _messages.asStateFlow()

    val subscribeState: StateFlow<SubscribeState> = rtcService.subscribeState

    private val _currentAdviserId = MutableStateFlow<String?>(null)
    private val _currentRoomId = MutableStateFlow<String?>(null)
    val currentRoomId: StateFlow<String?> = _currentRoomId.asStateFlow()

    private var eventSubscription: Job? = null

    init {
        initializeAndSubscribe()
        eventSubscription = rtcService.subscribe().onEach { reply ->
            _messages.update { it + Message(reply.toString(), isUser = false) }
        }
        .launchIn(viewModelScope)
    }

    private fun initializeAndSubscribe() {
        viewModelScope.launch {
            _currentAdviserId.value = dataStore.adviserIdFlow().firstOrNull()
            // todo JARG_ fix me! if null move user to login screen
            subscribeToEvents()
        }
    }

    private fun subscribeToEvents() {
        eventSubscription?.cancel()
        eventSubscription = rtcService.subscribe()
            .onEach { event ->
                println("ChatViewModel Received event: $event")
                when (event) {
                    is ChatStreamResponse.Event.Hello -> {
                        val deviceId = event.hello.device_id
                        println("ChatViewModel: Hello event received, deviceId: $deviceId")
                        viewModelScope.launch {
                            dataStore.saveDeviceId(deviceId)
                            println("ChatViewModel: DeviceId '$deviceId' saved.")
                        }
                    }
                    is ChatStreamResponse.Event.Assignee_changed -> {
                        val assigneeChangedEvent = event.assignee_changed
                        if (assigneeChangedEvent.adviser_id == _currentAdviserId.value && _currentAdviserId.value != null) {
                            val newRoomId = assigneeChangedEvent.room_id
                            _currentRoomId.value = newRoomId
                            println("ChatViewModel: Assignee_changed for current adviser, new room_id: $newRoomId")
                        } else {
                            println("ChatViewModel: Assignee_changed for another adviser or adviserId is null. Ignoring. Event adviser: ${assigneeChangedEvent.adviser_id}, current adviser: ${_currentAdviserId.value}")
                        }
                    }
                    is ChatStreamResponse.Event.Room_message_sent -> {
                        val roomMessageSentEvent = event.room_message_sent
                        if (roomMessageSentEvent.author_id != _currentAdviserId.value &&
                            roomMessageSentEvent.room_id == _currentRoomId.value &&
                            _currentAdviserId.value != null &&
                            _currentRoomId.value != null) {
                            _messages.update { currentMessages ->
                                currentMessages + Message(
                                    text = roomMessageSentEvent.message,
                                    isUser = false
                                )
                            }
                            println("ChatViewModel: Room_message_sent received and added to UI from author ${roomMessageSentEvent.author_id}")
                        } else {
                            println("ChatViewModel: Room_message_sent ignored. Author: ${roomMessageSentEvent.author_id}, Room: ${roomMessageSentEvent.room_id}. Current Adviser: ${_currentAdviserId.value}, Current Room: ${_currentRoomId.value}")
                        }
                    }
                    is ChatStreamResponse.Event.Error -> {
                        println("ChatViewModel Error event: ${event.error.reason}")
                        _messages.update { it + Message("Error: ${event.error.reason}", isUser = false) }
                    }
                    else -> {
                        println("ChatViewModel: Unhandled event type: ${event::class.simpleName}")
                    }
                }
            }
            .launchIn(viewModelScope)
    }

    fun send(text: String) {
        val currentRoom = _currentRoomId.value
        if (currentRoom == null) {
            println("ChatViewModel ERROR: Cannot send message, room_id is not set.")
            _messages.update { it + Message("Error: Cannot send message, no active room.", isUser = false) }
            return
        }

        _messages.update { it + Message(text, isUser = true) }
        viewModelScope.launch {
            try {
                rtcService.sendChat(
                    ChatStreamRequest(
                        ChatStreamRequest.Event.Room_send_message(
                            RoomSendMessage(
                                room_id = currentRoom,
                                body = text
                            )
                        )
                    )
                )
            } catch (e: Exception) {
                println("ChatViewModel ERROR sending message: ${e.message}")
            }
        }
    }

    override fun onCleared() {
        eventSubscription?.cancel()
    }
}
