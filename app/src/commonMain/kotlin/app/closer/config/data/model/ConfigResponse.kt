package app.closer.config.data.model

import kotlinx.serialization.Serializable

@Serializable
data class ConfigResponse(
    val apiVersion: String,
    val clusters: List<Cluster>
)

@Serializable
data class Cluster(
    val name: String,
    val orgs: List<Organization>,
    val url: String
)

@Serializable
data class Organization(
    val default: Boolean,
    val id: String,
    val name: String
)
