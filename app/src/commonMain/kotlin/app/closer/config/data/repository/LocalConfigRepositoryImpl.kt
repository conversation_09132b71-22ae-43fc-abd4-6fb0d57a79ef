package app.closer.config.data.repository

import app.closer.config.data.storage.ConfigDataStore
import app.closer.config.domain.model.OrganizationInfo
import app.closer.config.domain.model.SavedOrganization
import app.closer.config.domain.repository.LocalConfigRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.withContext

class LocalConfigRepositoryImpl(
    private val configDataStore: ConfigDataStore
) : LocalConfigRepository {

    override suspend fun saveSelectedOrganization(
        organizationInfo: OrganizationInfo,
        environment: String,
        environmentUrl: String
    ) = withContext(Dispatchers.IO) {
        println("LocalConfigRepository: Saving organization: ${organizationInfo.name} (${organizationInfo.id}) from $environment on ${Thread.currentThread().name}")

        configDataStore.saveSelectedOrganization(
            orgId = organizationInfo.id,
            orgName = organizationInfo.name,
            environment = environment,
            environmentUrl = environmentUrl,
            isDefault = organizationInfo.isDefault
        )

        println("LocalConfigRepository: Organization saved successfully")
    }

    override fun getSavedOrganizationFlow(): Flow<SavedOrganization?> {
        return combine(
            configDataStore.getSelectedOrgIdFlow(),
            configDataStore.getSelectedOrgNameFlow(),
            configDataStore.getSelectedEnvironmentFlow(),
            configDataStore.getSelectedEnvironmentUrlFlow(),
            configDataStore.getSelectedOrgIsDefaultFlow()
        ) { orgId, orgName, environment, environmentUrl, isDefault ->
            if (orgId != null && orgName != null && environment != null && environmentUrl != null) {
                SavedOrganization(
                    organizationInfo = OrganizationInfo(
                        id = orgId,
                        name = orgName,
                        isDefault = isDefault
                    ),
                    environment = environment,
                    environmentUrl = environmentUrl
                )
            } else {
                null
            }
        }.flowOn(Dispatchers.IO) // DataStore reads happen on IO dispatcher
    }
}
