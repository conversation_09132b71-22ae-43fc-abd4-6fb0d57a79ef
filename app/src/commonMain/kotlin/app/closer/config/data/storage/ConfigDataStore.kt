package app.closer.config.data.storage

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

private val SELECTED_ORG_ID = stringPreferencesKey("config_selected_org_id")
private val SELECTED_ORG_NAME = stringPreferencesKey("config_selected_org_name")
private val SELECTED_ENVIRONMENT = stringPreferencesKey("config_selected_environment")
private val SELECTED_ENVIRONMENT_URL = stringPreferencesKey("config_selected_environment_url")
private val SELECTED_ORG_IS_DEFAULT = booleanPreferencesKey("config_selected_org_is_default")

class ConfigDataStore(
    private val dataStore: DataStore<Preferences>
) {
    
    suspend fun saveSelectedOrganization(
        orgId: String,
        orgName: String,
        environment: String,
        environmentUrl: String,
        isDefault: Boolean
    ) {
        dataStore.edit { prefs ->
            prefs[SELECTED_ORG_ID] = orgId
            prefs[SELECTED_ORG_NAME] = orgName
            prefs[SELECTED_ENVIRONMENT] = environment
            prefs[SELECTED_ENVIRONMENT_URL] = environmentUrl
            prefs[SELECTED_ORG_IS_DEFAULT] = isDefault
        }
    }
    
    fun getSelectedOrgIdFlow(): Flow<String?> =
        dataStore.data.map { prefs -> prefs[SELECTED_ORG_ID] }
    
    fun getSelectedOrgNameFlow(): Flow<String?> =
        dataStore.data.map { prefs -> prefs[SELECTED_ORG_NAME] }
    
    fun getSelectedEnvironmentFlow(): Flow<String?> =
        dataStore.data.map { prefs -> prefs[SELECTED_ENVIRONMENT] }
    
    fun getSelectedEnvironmentUrlFlow(): Flow<String?> =
        dataStore.data.map { prefs -> prefs[SELECTED_ENVIRONMENT_URL] }
    
    fun getSelectedOrgIsDefaultFlow(): Flow<Boolean> =
        dataStore.data.map { prefs -> prefs[SELECTED_ORG_IS_DEFAULT] ?: false }
}
