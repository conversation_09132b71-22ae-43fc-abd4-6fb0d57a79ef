package app.closer.config.di

import app.closer.config.presentation.ConfigViewModel
import app.closer.config.data.api.ConfigApi
import app.closer.config.data.api.ConfigApiImpl
import app.closer.config.data.repository.RemoteConfigRepositoryImpl
import app.closer.config.data.repository.LocalConfigRepositoryImpl
import app.closer.config.data.storage.ConfigDataStore
import app.closer.config.data.storage.createAppDataStore
import app.closer.config.domain.repository.RemoteConfigRepository
import app.closer.config.domain.repository.LocalConfigRepository
import io.ktor.client.HttpClient
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.plugins.logging.LogLevel
import io.ktor.client.plugins.logging.Logger
import io.ktor.client.plugins.logging.Logging
import io.ktor.serialization.kotlinx.json.json
import kotlinx.serialization.json.Json
import org.koin.core.module.dsl.factoryOf
import org.koin.core.qualifier.named
import org.koin.dsl.bind
import org.koin.dsl.module

val configModule = module {

    single<HttpClient> {
        HttpClient {
            install(ContentNegotiation) {
                json(Json {
                    prettyPrint = true
                    isLenient = true
                    ignoreUnknownKeys = true
                })
            }

            install(Logging) {
                logger = object : Logger {
                    override fun log(message: String) {
                        println("HTTP Client: $message")
                    }
                }
                level = LogLevel.INFO
            }
        }
    }

    factory<ConfigApi> {
        ConfigApiImpl(get())
    }

    single {
        // Get context if available (Android), null for iOS
        val context: Any? = getOrNull(qualifier = named("applicationContext"))
        createAppDataStore(context ?: Any()) // Pass dummy object for iOS
    }

    single {
        ConfigDataStore(get())
    }

    factoryOf(::RemoteConfigRepositoryImpl) bind RemoteConfigRepository::class

    factory<LocalConfigRepository> {
        LocalConfigRepositoryImpl(get())
    }

    factoryOf(::ConfigViewModel)
}
