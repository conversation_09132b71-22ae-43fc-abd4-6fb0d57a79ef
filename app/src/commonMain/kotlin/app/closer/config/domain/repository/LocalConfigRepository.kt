package app.closer.config.domain.repository

import app.closer.config.domain.model.OrganizationInfo
import app.closer.config.domain.model.SavedOrganization
import kotlinx.coroutines.flow.Flow

interface LocalConfigRepository {
    suspend fun saveSelectedOrganization(
        organizationInfo: OrganizationInfo,
        environment: String,
        environmentUrl: String
    )
    
    fun getSavedOrganizationFlow(): Flow<SavedOrganization?>
}
