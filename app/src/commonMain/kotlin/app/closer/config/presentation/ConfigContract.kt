package app.closer.config.presentation

import app.closer.config.domain.model.Config
import app.closer.config.domain.model.OrganizationInfo
import app.closer.config.domain.model.SavedOrganization

object ConfigContract {
    
    data class UiState(
        val isLoading: Boolean = false,
        val config: Config? = null,
        val error: String? = null,
        val apiKey: String = "9be4f134-992c-4429-9d26-8af37649f402",
        val selectedOrganization: SelectedOrganization? = null,
        val savedOrganization: SavedOrganization? = null
    )

    data class SelectedOrganization(
        val organizationInfo: OrganizationInfo,
        val environment: String, // e.g., "stage", "indus"
        val environmentUrl: String // e.g., "https://stage.closer.app"
    )
    
    sealed interface Event {

        data object LoadConfig : Event
        data class UpdateApiKey(val apiKey: String) : Event
        data class SelectOrganization(val organization: SelectedOrganization) : Event
        data object ClearError : Event
    }

    sealed interface Effect {

        data class ShowError(val message: String) : Effect
    }
}
