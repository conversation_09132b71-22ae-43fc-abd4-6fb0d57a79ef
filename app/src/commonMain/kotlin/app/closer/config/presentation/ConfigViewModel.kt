package app.closer.config.presentation

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import app.closer.config.domain.repository.RemoteConfigRepository
import app.closer.config.domain.repository.LocalConfigRepository
import app.closer.config.presentation.ConfigContract.Effect
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch

class ConfigViewModel(
    private val remoteConfigRepository: RemoteConfigRepository,
    private val localConfigRepository: LocalConfigRepository
) : ViewModel() {
    
    var uiState by mutableStateOf(ConfigContract.UiState())
        private set

    private val _effect: Channel<Effect> = Channel()
    val effect = _effect.receiveAsFlow()
    
    init {
        onEvent(ConfigContract.Event.LoadConfig)

        localConfigRepository.observeSelectedOrganization()
            .onEach { savedOrg ->
                uiState = uiState.copy(savedOrganization = savedOrg)
                if (savedOrg != null) {
                    println("ConfigViewModel: Loaded saved organization: ${savedOrg.organizationInfo.name} from ${savedOrg.environment}")

                    uiState.config?.let { config ->
                        checkAndSelectSavedOrganization(config)
                    }
                }
            }
            .launchIn(viewModelScope)
    }
    
    fun onEvent(event: ConfigContract.Event) {
        viewModelScope.launch {
            when (event) {
                is ConfigContract.Event.LoadConfig -> {
                    loadConfig()
                }

                is ConfigContract.Event.UpdateApiKey -> {
                    updateApiKey(event.apiKey)
                }

                is ConfigContract.Event.SelectOrganization -> {
                    selectOrganization(event.organization)
                }

                is ConfigContract.Event.ClearError -> {
                    clearError()
                }
            }
        }
    }
    
    private suspend fun loadConfig() {
        uiState = uiState.copy(isLoading = true, error = null)

        remoteConfigRepository.getConfig(uiState.apiKey)
            .onSuccess { config ->
                uiState = uiState.copy(
                    isLoading = false,
                    config = config,
                    error = null
                )

                checkAndSelectSavedOrganization(config)
            }
            .onFailure { error ->
                val errorMessage = error.message ?: "Unknown error occurred"
                uiState = uiState.copy(
                    isLoading = false,
                    error = errorMessage
                )
                _effect.send(Effect.ShowError(errorMessage))
            }
    }
    
    private fun updateApiKey(apiKey: String) {
        uiState = uiState.copy(apiKey = apiKey)
    }
    
    private suspend fun selectOrganization(organization: ConfigContract.SelectedOrganization) {
        uiState = uiState.copy(selectedOrganization = organization)
        try {
            localConfigRepository.saveSelectedOrganization(
                organizationInfo = organization.organizationInfo,
                environment = organization.environment,
                environmentUrl = organization.environmentUrl
            )
        } catch (e: Exception) {
            _effect.send(Effect.ShowError("Failed to save configuration: ${e.message}"))
        }
    }

    private fun clearError() {
        uiState = uiState.copy(error = null)
    }

    private fun checkAndSelectSavedOrganization(config: app.closer.config.domain.model.Config) {
        val savedOrg = uiState.savedOrganization ?: return

        config.clusters.forEach { cluster ->
            cluster.organizations.forEach { org ->
                if (org.id == savedOrg.organizationInfo.id &&
                    cluster.name == savedOrg.environment &&
                    cluster.url == savedOrg.environmentUrl) {

                    val selectedOrganization = ConfigContract.SelectedOrganization(
                        organizationInfo = org,
                        environment = cluster.name,
                        environmentUrl = cluster.url
                    )

                    uiState = uiState.copy(selectedOrganization = selectedOrganization)
                    return
                }
            }
        }
    }
}
