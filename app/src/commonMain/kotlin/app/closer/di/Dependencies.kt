package app.closer.di

import app.closer.chat.chatModule
import app.closer.config.di.configModule
import app.closer.start.startModule
import org.koin.core.context.startKoin
import org.koin.core.qualifier.named
import org.koin.dsl.KoinAppDeclaration
import org.koin.dsl.module

fun initKoin(applicationContext: Any? = null, config: KoinAppDeclaration? = null) {
    startKoin {
        config?.invoke(this)
        modules(
            createAppModule(applicationContext),
            startModule,
            chatModule,
            configModule,
        )
    }
}

private fun createAppModule(applicationContext: Any?) = module {
    // Only provide applicationContext for Android
    // iOS doesn't need it for DataStore
    if (applicationContext != null) {
        single<Any>(named("applicationContext")) { applicationContext }
    }
}
