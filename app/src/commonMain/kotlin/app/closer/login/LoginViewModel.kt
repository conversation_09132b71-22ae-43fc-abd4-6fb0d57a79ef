package app.closer.login

import app.closer.LoginRequest
import app.closer.sdk.Closer
import app.closer.sdk.saveAdviserId
import app.closer.sdk.saveApiKey
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class LoginViewModel {
    private val viewModelScope = CoroutineScope(Dispatchers.Main + SupervisorJob())

    private val rtcService = Closer.getRTCService()
    private val dataStore = Closer.getDataStore()

    sealed class LoginUiState {
        data object Idle : LoginUiState()
        data object Loading : LoginUiState()
        data class Success(val apiKey: String, val adviserId: String) : LoginUiState()
        data class Error(val message: String) : LoginUiState()
    }

    private val _loginState = MutableStateFlow<LoginUiState>(LoginUiState.Idle)
    val loginState: StateFlow<LoginUiState> = _loginState.asStateFlow()

    fun performLogin(username: String, password: String) {
        _loginState.value = LoginUiState.Loading
        viewModelScope.launch {
            try {
                val request = LoginRequest(email = username, password = password)
                val response = rtcService.login(request)

                val apiKey = response.api_key
                val adviserId = response.id

                if (apiKey.isNotBlank() && adviserId.isNotBlank()) {
                    dataStore.saveApiKey(apiKey)
                    dataStore.saveAdviserId(adviserId)
                    _loginState.value = LoginUiState.Success(apiKey, adviserId)
                } else {
                    _loginState.value = LoginUiState.Error("Invalid API key or Adviser ID received.")
                }

            } catch (e: Exception) {
                _loginState.value = LoginUiState.Error(e.message ?: "Unknown login error")
            }
        }
    }

    fun clear() {
        viewModelScope.cancel()
    }
}