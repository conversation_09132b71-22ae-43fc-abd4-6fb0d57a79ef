package app.closer.navigation

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import app.closer.chat.ChatScreen
import app.closer.config.presentation.ConfigScreen
import app.closer.login.LoginScreen
import app.closer.splash.SplashScreen
import app.closer.start.StartScreen

@Composable
fun AppNavigationHost() {
    val navController = rememberNavController()
    AppNavigation(navController = navController)
}

@Composable
fun AppNavigation(
    navController: NavHostController
) {
    NavHost(
        navController = navController,
        startDestination = Navigation.Routes.START
    ) {
        composable(route = Navigation.Routes.START) {
            StartScreen(navController)
        }

        composable(route = Navigation.Routes.ADVISER) {
            SplashScreen(
                onUserAuthenticated = {
                    navController.navigate(Navigation.Routes.CHAT) {
                        popUpTo(Navigation.Routes.ADVISER) { inclusive = true }
                    }
                },
                onUserNotAuthenticated = {
                    navController.navigate(Navigation.Routes.LOGIN) {
                        popUpTo(Navigation.Routes.ADVISER) { inclusive = true }
                    }
                }
            )
        }
        composable(route = Navigation.Routes.LOGIN) {
            LoginScreen(
                onLoginSuccess = {
                    navController.navigate(Navigation.Routes.CHAT) {
                        popUpTo(Navigation.Routes.LOGIN) { inclusive = true }
                    }
                }
            )
        }
        composable(route = Navigation.Routes.CHAT) {
            ChatScreen()
        }
        composable(route = Navigation.Routes.LEAD) {
            LeadPlaceholderScreen(navController)
        }
        composable(route = Navigation.Routes.CONFIG) {
            ConfigScreen()
        }
    }
}

// Placeholder screens - TODO: Replace with actual implementations
@Composable
private fun LeadPlaceholderScreen(navController: NavHostController) {
    PlaceholderScreen(
        title = "Lead Screen",
        description = "TODO: Implement Lead functionality",
        navController = navController
    )
}

@Composable
private fun PlaceholderScreen(
    title: String,
    description: String,
    navController: NavHostController
) {
    MaterialTheme {
        Scaffold { paddingValues ->
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
                    .padding(24.dp),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                Text(
                    text = description,
                    style = MaterialTheme.typography.bodyLarge,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(bottom = 32.dp)
                )

                Button(
                    onClick = { navController.popBackStack() }
                ) {
                    Text("Go Back")
                }
            }
        }
    }
}
