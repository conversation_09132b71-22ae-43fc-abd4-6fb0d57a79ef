package app.closer.splash

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier

@Composable
fun SplashScreen(
    splashViewModel: SplashViewModel = remember { SplashViewModel() },
    onUserAuthenticated: () -> Unit,
    onUserNotAuthenticated: () -> Unit
) {
    val decision by splashViewModel.navigationDecision.collectAsState()

    LaunchedEffect(decision) {
        when (decision) {
            SplashViewModel.NavigationDecision.GoToChat -> onUserAuthenticated()
            SplashViewModel.NavigationDecision.GoToLogin -> onUserNotAuthenticated()
            SplashViewModel.NavigationDecision.Loading -> { /* Do nothing, waiting */ }
        }
    }

    Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
        if (decision == SplashViewModel.NavigationDecision.Loading) {
            CircularProgressIndicator()
        }
    }
}