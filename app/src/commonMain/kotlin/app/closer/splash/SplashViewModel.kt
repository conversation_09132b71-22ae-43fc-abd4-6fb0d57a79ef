package app.closer.splash

import app.closer.sdk.Closer
import app.closer.sdk.adviserIdFlow
import app.closer.sdk.apiKeyFlow
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.launch

class SplashViewModel {
    private val viewModelScope = CoroutineScope(Dispatchers.Main + SupervisorJob())

    private val dataStore = Closer.getDataStore()

    sealed class NavigationDecision {
        data object Loading : NavigationDecision()
        data object GoToLogin : NavigationDecision()
        data object GoToChat : NavigationDecision()
    }

    private val _navigationDecision = MutableStateFlow<NavigationDecision>(NavigationDecision.Loading)
    val navigationDecision: StateFlow<NavigationDecision> = _navigationDecision.asStateFlow()

    init {
        checkUserAuthentication()
    }

    private fun checkUserAuthentication() {
        viewModelScope.launch {
            val apiKey = dataStore.apiKeyFlow().firstOrNull()

            val adviserId = dataStore.adviserIdFlow().firstOrNull()

            if (!apiKey.isNullOrBlank() && !adviserId.isNullOrBlank()) {
                _navigationDecision.value = NavigationDecision.GoToChat
            } else {
                _navigationDecision.value = NavigationDecision.GoToLogin
            }
        }
    }

    fun clear() {
        viewModelScope.cancel()
    }
}