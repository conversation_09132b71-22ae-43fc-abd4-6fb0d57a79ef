package app.closer.start

import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.navigation.NavHostController
import app.closer.navigation.Navigation
import kotlinx.coroutines.flow.collectLatest
import org.koin.compose.viewmodel.koinViewModel

@Composable
fun StartScreen(
    navController: NavHostController,
    modifier: Modifier = Modifier,
    viewModel: StartViewModel = koinViewModel()
) {
    LaunchedEffect(viewModel.effect) {
        viewModel.effect.collectLatest { effect ->
            when (effect) {
                is StartContract.Effect.NavigateToLead -> {
                    navController.navigate(Navigation.Routes.LEAD)
                }
                is StartContract.Effect.NavigateToAdviser -> {
                    navController.navigate(Navigation.Routes.ADVISER)
                }
                is StartContract.Effect.NavigateToConfig -> {
                    navController.navigate(Navigation.Routes.CONFIG)
                }
            }
        }
    }
    

    Scaffold(
        modifier = modifier
    ) { paddingValues ->
        StartScreenContent(
            onEvent = viewModel::onEvent,
            modifier = Modifier.padding(paddingValues)
        )
    }
}
