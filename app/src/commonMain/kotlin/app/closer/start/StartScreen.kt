package app.closer.start

import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.navigation.NavHostController
import app.closer.navigation.Routes
import kotlinx.coroutines.flow.collectLatest
import org.koin.compose.viewmodel.koinViewModel

@Composable
fun StartScreen(
    navController: NavHostController,
    modifier: Modifier = Modifier,
    viewModel: StartViewModel = koinViewModel()
) {
    LaunchedEffect(viewModel.effect) {
        viewModel.effect.collectLatest { effect ->
            when (effect) {
                is StartContract.Effect.NavigateToLead -> {
                    navController.navigate(Routes.LEAD)
                }
                is StartContract.Effect.NavigateToAdviser -> {
                    navController.navigate(Routes.ADVISER)
                }
                is StartContract.Effect.NavigateToConfig -> {
                    navController.navigate(Routes.CONFIG)
                }
            }
        }
    }
    

    Scaffold(
        modifier = modifier
    ) { paddingValues ->
        StartScreenContent(
            onEvent = viewModel::onEvent,
            modifier = Modifier.padding(paddingValues)
        )
    }
}
