package app.closer.start

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import app.closer.start.StartContract.Effect
import app.closer.start.StartContract.Event
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch

class StartViewModel : ViewModel() {

    private val _effect: Channel<Effect> = Channel()
    val effect = _effect.receiveAsFlow()

    fun onEvent(event: Event) {
        viewModelScope.launch {
            when (event) {
                is Event.OnLeadClicked -> _effect.send(Effect.NavigateToLead)
                is Event.OnAdviserClicked -> _effect.send(Effect.NavigateToAdviser)
                is Event.OnConfigClicked -> _effect.send(Effect.NavigateToConfig)
            }
        }
    }
}
