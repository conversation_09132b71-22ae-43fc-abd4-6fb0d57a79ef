# MyHealth - Kotlin Multiplatform Architecture Documentation

## Project Overview

MyHealth is a Kotlin Multiplatform (KMP) application targeting Android and iOS platforms. The application serves as a health management platform that allows users to authenticate and connect with various healthcare providers (currently focusing on LuxMed integration).

## Technology Stack

### Core Technologies
- **Kotlin Multiplatform**: Shared business logic across platforms
- **Jetpack Compose Multiplatform**: UI framework for both Android and iOS
- **Firebase**: Authentication and crash reporting
- **Koin**: Dependency injection framework
- **Coroutines & Flow**: Asynchronous programming and reactive streams
- **Kermit**: Multiplatform logging

### Platform-Specific
- **Android**: Native Android app with Compose UI
- **iOS**: SwiftUI wrapper around Compose Multiplatform

## Project Structure

```
MyHealth-KotlinMultiPlatform/
├── myHealthApp/                 # Main application module
├── login/                       # Login feature module
│   ├── login-data/             # Data layer
│   ├── login-domain/           # Domain layer  
│   └── login-presentation/     # Presentation layer
├── home/                       # Home feature module
│   ├── home-domain/           # Domain layer
│   └── home-presentation/     # Presentation layer
├── common/                     # Shared utilities
├── iosApp/                     # iOS application entry point
└── .docs/                      # Documentation
```

## Architecture Pattern

The project follows **Clean Architecture** principles with **MVVM (Model-View-ViewModel)** pattern:

### Layer Separation
1. **Presentation Layer**: UI components, ViewModels, and navigation
2. **Domain Layer**: Business logic, use cases, and domain models
3. **Data Layer**: Repositories, data sources, and external API integrations

### Modular Architecture
The application is organized into feature-based modules:
- Each feature has its own domain, data, and presentation layers
- Clear separation of concerns and dependencies
- Scalable and maintainable codebase

## Core Modules

### 1. myHealthApp Module
**Purpose**: Main application entry point and navigation setup

**Key Components**:
- `App.kt`: Root composable with navigation setup
- `AppNavigation.kt`: Navigation graph configuration
- `MainViewModel.kt`: Handles initial authentication state
- `MyHealthDependency.kt`: Dependency injection initialization

**Dependencies**: All feature modules (login, home)

### 2. Login Module

#### login-domain
**Purpose**: Authentication business logic

**Key Components**:
- `LoginGateway`: Interface for authentication operations
- `LoginUseCase`: Handle user sign-in
- `CreateAccountUseCase`: Handle user registration
- `IsSignInUseCase`: Check authentication status
- `CreateAccountSpecification`: Validation rules

#### login-data
**Purpose**: Authentication data management

**Key Components**:
- `FirebaseLoginGateway`: Firebase authentication implementation
- Firebase Auth integration

#### login-presentation
**Purpose**: Authentication UI and user interactions

**Key Components**:
- `SignInScreen` & `SignInViewModel`: Sign-in functionality
- `SignUpScreen` & `SignUpViewModel`: Registration functionality
- Contract classes for UI state management

### 3. Home Module

#### home-domain
**Purpose**: Health data business logic

**Key Components**:
- `MedicalRecordsRepository`: Interface for medical data
- `FetchAllMedicalRecordsUseCase`: Retrieve medical records

#### home-presentation
**Purpose**: Home and provider management UI

**Key Components**:
- `HomeScreen`: Main dashboard (currently minimal)
- `AddProviderScreen`: Healthcare provider selection
- `LoginProviderScreen`: Provider-specific authentication
- `MyHealthWebView`: WebView for provider login (LuxMed)

### 4. Common Module
**Purpose**: Shared utilities and platform-specific implementations

**Key Components**:
- `AppLogger`: Platform-specific logging (Android Log, iOS NSLog)
- Shared utilities and extensions

## Navigation Flow

```
App Launch → MainViewModel checks auth status
    ↓
If not authenticated → SignInScreen
    ↓
SignInScreen → SignUpScreen (optional)
    ↓
After authentication → AddProviderScreen
    ↓
Select Provider → LoginProviderScreen (WebView)
    ↓
Provider authenticated → HomeScreen
```

## Dependency Injection

The project uses **Koin** for dependency injection with modular setup:

```kotlin
initKoin {
    modules(
        *loginPresentationModule,
        *homePresentationModule,
        loginDomainModule,
        loginDataModule,
        mainModule,
    )
}
```

### Module Structure:
- Each layer has its own DI module
- ViewModels are injected using `koinViewModel()`
- Use cases and repositories are factory-scoped
- Firebase services are singleton-scoped

## Platform-Specific Implementation

### Android
- `MainActivity`: Single activity with Compose UI
- `MyHealthApplication`: Application class with Firebase and Koin initialization
- Native WebView implementation for provider authentication

### iOS
- `iOSApp.swift`: SwiftUI app entry point
- `ContentView.swift`: Compose UI wrapper
- `MainViewController.kt`: Compose UI controller
- Native WKWebView implementation for provider authentication

## State Management

### UI State Management
- **MVI-like pattern** with sealed classes for effects
- `UiState` data classes for screen state
- `Effect` sealed classes for side effects (navigation, errors)
- Reactive streams using Kotlin Flow

### Example Pattern:
```kotlin
// Contract
data class UiState(val email: String = "", val password: String = "")
sealed class Effect {
    object NavigateToHome : Effect()
    object NavigateToSignUp : Effect()
}

// ViewModel
private val _effect: Channel<Effect> = Channel()
val effect = _effect.receiveAsFlow()
var uiState = mutableStateOf(UiState())
```

## Key Features

### 1. Authentication
- Firebase-based email/password authentication
- Account creation with validation
- Persistent authentication state

### 2. Healthcare Provider Integration
- Currently supports LuxMed integration
- WebView-based provider authentication
- Token extraction and management
- Extensible for additional providers

### 3. Cross-Platform UI
- Shared Compose Multiplatform UI
- Platform-specific WebView implementations
- Consistent user experience across platforms

## External Integrations

### Firebase
- **Authentication**: User sign-in/sign-up
- **Crashlytics**: Error reporting and analytics
- Platform-specific initialization

### LuxMed Integration
- WebView-based authentication flow
- Token extraction from web session
- Structured authentication data model

## Build Configuration

### Gradle Setup
- Version catalog for dependency management
- Multiplatform module configuration
- Platform-specific build variants
- Compose compiler integration

### Key Dependencies
- Kotlin 2.0.0
- Compose Multiplatform 1.6.11
- Koin 3.6.0-Beta4
- Firebase Multiplatform 1.12.0
- Kermit 2.0.4

## Testing Strategy

### Current State
- Basic test structure in place
- Unit test placeholders in domain modules
- Test dependencies configured

### Recommended Improvements
- Unit tests for use cases and ViewModels
- Integration tests for repositories
- UI tests for critical user flows
- Mock implementations for external services

## Security Considerations

### Authentication
- Firebase handles secure authentication
- Token-based session management
- Secure storage of authentication state

### Provider Integration
- HTTPS-only communication
- Secure token extraction
- Encrypted local storage (recommended)

## Future Enhancements

### Architecture
1. **Repository Pattern**: Implement data repositories for medical records
2. **Offline Support**: Local database integration (Room/SQLDelight)
3. **Error Handling**: Comprehensive error management system
4. **Loading States**: Better UX with loading indicators

### Features
1. **Multiple Providers**: Extend beyond LuxMed
2. **Medical Records**: Display and manage health data
3. **Notifications**: Health reminders and updates
4. **Data Sync**: Cloud synchronization of health data

### Technical Debt
1. **Testing**: Comprehensive test coverage
2. **Documentation**: API documentation and code comments
3. **Performance**: Optimize WebView and data loading
4. **Accessibility**: Improve accessibility support

## Development Guidelines

### Code Organization
- Feature-based module structure
- Clear separation of concerns
- Consistent naming conventions
- Proper dependency management

### Best Practices
- Use sealed classes for state management
- Implement proper error handling
- Follow Clean Architecture principles
- Maintain platform-specific implementations minimal

### Contributing
- Follow existing architectural patterns
- Add appropriate tests for new features
- Update documentation for significant changes
- Ensure cross-platform compatibility

---

*This documentation reflects the current state of the MyHealth Kotlin Multiplatform project and should be updated as the architecture evolves.*
