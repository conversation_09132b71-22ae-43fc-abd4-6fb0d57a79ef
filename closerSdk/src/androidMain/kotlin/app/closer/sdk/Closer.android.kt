package app.closer.sdk

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.preferencesDataStore
import app.closer.sdk.messaging.GrpcRTCService
import app.closer.sdk.messaging.RTCService

private val Context.createStore: DataStore<Preferences> by preferencesDataStore(name = "closer_sdk_settings")

actual object Closer {
    private lateinit var internalDataStore: DataStore<Preferences>
    private lateinit var internalRtcService: RTCService

    private var isInitialized = false

    actual fun initialize(config: Config) {
        if (isInitialized) {
            return
        }

        val context = config.context() as? Context
            ?: throw IllegalArgumentException("Android context provider did not return a Context for Closer SDK initialization")

        internalDataStore = context.applicationContext.createStore
        internalRtcService = GrpcRTCService.Builder(config.address())
            .port(config.port())
            .plainTextCommunication(config.plainTextCommunication())
            .dataStore(internalDataStore)
            .build()

        isInitialized = true
    }

    actual fun getDataStore(): DataStore<Preferences> {
        if (!isInitialized) {
            throw IllegalStateException("Closer SDK not initialized. Call Closer.initialize() first.")
        }
        return internalDataStore
    }

    actual fun getRTCService(): RTCService {
        if (!isInitialized) {
            throw IllegalStateException("Closer SDK not initialized. Call Closer.initialize() first.")
        }
        return internalRtcService
    }

}
