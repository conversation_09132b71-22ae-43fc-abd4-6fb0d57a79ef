package app.closer.sdk

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import app.closer.sdk.messaging.RTCService

expect object Closer {
    fun initialize(config: Config)
    fun getDataStore(): DataStore<Preferences>
    fun getRTCService(): RTCService
}

interface Config {
    fun context(): Any
    fun address(): String
    fun port(): Int = 443
    fun plainTextCommunication(): Boolean = false
}
