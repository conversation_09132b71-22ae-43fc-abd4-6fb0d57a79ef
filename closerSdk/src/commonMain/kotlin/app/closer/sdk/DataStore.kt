package app.closer.sdk

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

val API_KEY = stringPreferencesKey("api_key")
val DEVICE_ID = stringPreferencesKey("device_id")
val ADVISER_ID = stringPreferencesKey("adviser_id")
val ROOM_ID = stringPreferencesKey("room_id")

suspend fun DataStore<Preferences>.saveApiKey(apiKey: String) {
    edit { prefs ->
        prefs[API_KEY] = apiKey
    }
}

suspend fun DataStore<Preferences>.saveDeviceId(deviceId: String) {
    edit { prefs ->
        prefs[DEVICE_ID] = deviceId
    }
}

suspend fun DataStore<Preferences>.saveAdviserId(adviserId: String) {
    edit { prefs ->
        prefs[ADVISER_ID] = adviserId
    }
}

suspend fun DataStore<Preferences>.saveRoomId(roomId: String) {
    edit { prefs ->
        prefs[ROOM_ID] = roomId
    }
}

fun DataStore<Preferences>.apiKeyFlow(): Flow<String?> =
    data.map { prefs -> prefs[API_KEY] }

fun DataStore<Preferences>.deviceIdFlow(): Flow<String?> =
    data.map { prefs -> prefs[DEVICE_ID] }

fun DataStore<Preferences>.adviserIdFlow(): Flow<String?> =
    data.map { prefs -> prefs[ADVISER_ID] }

fun DataStore<Preferences>.roomIdFlow(): Flow<String?> =
    data.map { prefs -> prefs[ROOM_ID] }
