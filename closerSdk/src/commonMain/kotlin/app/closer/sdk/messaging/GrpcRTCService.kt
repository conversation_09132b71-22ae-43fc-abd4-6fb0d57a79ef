package app.closer.sdk.messaging

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import app.closer.AnswerCallRequest
import app.closer.ChatServiceStub
import app.closer.ChatStreamRequest
import app.closer.ChatStreamResponse
import app.closer.ErrorEvent
import app.closer.GetCallRequest
import app.closer.GetCallResponse
import app.closer.InputHeartbeat
import app.closer.LeaveCallRequest
import app.closer.LoginRequest
import app.closer.LoginResponse
import app.closer.sdk.apiKeyFlow
import app.closer.sdk.deviceIdFlow
import app.closer.sdk.messaging.RTCService.SubscribeState
import co.touchlab.kermit.Logger
import io.github.timortel.kmpgrpc.core.CallInterceptor
import io.github.timortel.kmpgrpc.core.Channel
import io.github.timortel.kmpgrpc.core.MethodDescriptor
import io.github.timortel.kmpgrpc.core.metadata.Key
import io.github.timortel.kmpgrpc.core.metadata.Metadata
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlin.coroutines.cancellation.CancellationException
import kotlin.jvm.JvmStatic

private const val TAG = "GrpcRTCService"

class GrpcRTCService private constructor(
    address: String,
    port: Int,
    plainTextCommunication: Boolean,
    dataStore: DataStore<Preferences>?
) : RTCService {

    private val callInterceptor = object : CallInterceptor {
        override fun onStart(methodDescriptor: MethodDescriptor, metadata: Metadata): Metadata {
            val apiKey = runBlocking { dataStore?.apiKeyFlow()?.firstOrNull() }
            val deviceId = runBlocking { dataStore?.deviceIdFlow()?.firstOrNull() }
            var outgoingMetadata = metadata

            if (!apiKey.isNullOrBlank()) {
                outgoingMetadata = outgoingMetadata.withEntry(Key.AsciiKey("x-api-key") to apiKey)
            } else {
                Logger.w(TAG) { "Interceptor: API Key is null. Skipping x-api-key header." }
            }

            if (!deviceId.isNullOrBlank()) {
                outgoingMetadata = outgoingMetadata.withEntry(Key.AsciiKey("device_id") to deviceId)
            } else {
                Logger.w(TAG) { "Interceptor: Device ID is null. Skipping device_id header." }
            }

            return super.onStart(
                methodDescriptor,
                outgoingMetadata
            )
        }
    }

    private val channel = Channel.Builder
        .forAddress(address, port)
        .apply { if (plainTextCommunication) usePlaintext() }
        .withInterceptors(callInterceptor)
        .build()

    private val serviceStub = ChatServiceStub(channel)
    private val scope = CoroutineScope(Dispatchers.IO)

    private val _outgoingRequests = MutableSharedFlow<ChatStreamRequest>()

    private val _subscribeState = MutableStateFlow(SubscribeState.Unsubscribed)
    override val subscribeState: StateFlow<SubscribeState> = _subscribeState.asStateFlow()

    override suspend fun login(request: LoginRequest): LoginResponse {
        return serviceStub.Login(request).also { Logger.i { "Login response: $it" } }
    }

    override fun subscribe(): Flow<ChatStreamResponse.Event> = flow {
        if (_subscribeState.value == SubscribeState.Subscribed || _subscribeState.value == SubscribeState.Subscribing) {
            Logger.w(TAG) { "Already connected. Unnecessary call to subscribe." }
            return@flow
        }

        try {
            _subscribeState.value = SubscribeState.Subscribing
            Logger.i(TAG) { "Connecting..." }

            serviceStub
                .ChatStream(_outgoingRequests)
                .collect { response ->
                    if (_subscribeState.value != SubscribeState.Subscribed) {
                        _subscribeState.value = SubscribeState.Subscribed
                        Logger.i(TAG) { "Connection established. Listening..." }
                    }

                    when (val rsp = response.event) {
                        is ChatStreamResponse.Event.Output_heartbeat -> {
                            val request = ChatStreamRequest(
                                ChatStreamRequest.Event.Input_heartbeat(
                                    input_heartbeat = InputHeartbeat()
                                )
                            )
                            _outgoingRequests.emit(request)
                        }
                        else -> {
                            Logger.i(TAG) { "New response from server: $rsp" }
                            emit(rsp)
                        }
                    }
                }
        } catch (e: Exception) {
            if (e is CancellationException) throw e
            Logger.e(TAG) { "gRPC connection error: ${e.message}" }
            _subscribeState.value = SubscribeState.Failed
            emit(
                ChatStreamResponse.Event.Error(
                    error = ErrorEvent(e.message ?: "gRPC connection error")
                )
            )
        } finally {
            if (_subscribeState.value != SubscribeState.Failed) {
                _subscribeState.value = SubscribeState.Unsubscribed
                Logger.i(TAG) { "Disconnected" }
            }
        }
    }

    override suspend fun sendChat(request: ChatStreamRequest) {
        if (_subscribeState.value != SubscribeState.Subscribed) {
            Logger.w(TAG) { "No connection, cannot send the message" }
            return
        }

        _outgoingRequests.emit(request)
    }

    override suspend fun getCall(request: GetCallRequest): GetCallResponse {
        return serviceStub.GetCall(request)
    }

    override suspend fun answerCall(request: AnswerCallRequest) {
        if (_subscribeState.value != SubscribeState.Subscribed) {
            Logger.w(TAG) { "No connection, cannot answer te call" }
            return
        }

        serviceStub.AnswerCall(request)
    }

    override suspend fun leaveCall(request: LeaveCallRequest) {
        if (_subscribeState.value != SubscribeState.Subscribed) {
            Logger.w(TAG) { "No connection, cannot answer te call" }
            return
        }

        serviceStub.LeaveCall(request)
    }

    fun closeChannel() {
        scope.launch {
            channel.shutdownNow()
        }
    }

    class Builder(private val address: String) {
        private var port: Int = 443
        private var plainTextCommunication: Boolean = false
        private var dataStore: DataStore<Preferences>? = null

        fun port(port: Int) = apply { this.port = port }
        fun plainTextCommunication(plainText: Boolean) = apply { this.plainTextCommunication = plainText }
        fun dataStore(dataStore: DataStore<Preferences>?) = apply { this.dataStore = dataStore }

        fun build(): GrpcRTCService {
            return GrpcRTCService(address, port, plainTextCommunication, dataStore)
        }
    }

    companion object {
        @JvmStatic
        fun builder(address: String): Builder {
            return Builder(address)
        }
    }
}
