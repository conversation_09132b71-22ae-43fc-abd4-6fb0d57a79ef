package app.closer.sdk.messaging

import app.closer.AnswerCallRequest
import app.closer.ChatStreamRequest
import app.closer.ChatStreamResponse
import app.closer.GetCallRequest
import app.closer.GetCallResponse
import app.closer.LeaveCallRequest
import app.closer.LoginRequest
import app.closer.LoginResponse
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.StateFlow

interface RTCService {
    val subscribeState: StateFlow<SubscribeState>

    suspend fun login(request: LoginRequest): LoginResponse

    fun subscribe(): Flow<ChatStreamResponse.Event>

    suspend fun sendChat(request: ChatStreamRequest)
    suspend fun getCall(request: GetCallRequest): GetCallResponse
    suspend fun answerCall(request: AnswerCallRequest)
    suspend fun leaveCall(request: LeaveCallRequest)

    enum class SubscribeState {
        Subscribed,
        Subscribing,
        Unsubscribed,
        Failed
    }
}
