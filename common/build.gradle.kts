import org.jetbrains.kotlin.gradle.dsl.JvmTarget

plugins {
    kotlin("multiplatform")
    alias(libs.plugins.grpc.kmp)
}

kotlin {
    jvm {
        compilerOptions {
            jvmTarget.set(JvmTarget.JVM_21)
            freeCompilerArgs.add("-Xjsr305=strict")
        }
    }

    sourceSets {
        commonMain {
            dependencies {
                implementation(kotlin("stdlib-common"))
                implementation(libs.kotlin.coroutines)
            }
        }
    }
}

kmpGrpc {
    android()
    common()
    jvm()

    includeWellKnownTypes = true

    protoSourceFolders = project.files("../protos/src/main/proto")
}
