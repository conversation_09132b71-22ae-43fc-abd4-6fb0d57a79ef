[versions]
grpc = "1.73.0"
grpc-kmp = "1.0.0"
grpc-kotlin = "1.4.3"
janino = "3.1.12"
kotlin = "2.1.21"
kotlin-coroutines = "1.10.2"
kotlin-logging = "7.0.7"
kotlin-serialization = "1.8.1"
ktor = "3.2.2"
logback = "1.5.18"
logstash-logback = "8.1"
protobuf = "4.31.1"
protobuf-plugin = "0.9.5"
slf4j = "2.0.17"
agp = "8.9.2"
compose = "1.8.2"
compose-material3 = "1.3.2"
androidx-activityCompose = "1.8.0"
kotlin-version = "2.1.21"
kotlin-stdlib = "2.1.21"
kotlin-test = "2.1.21"
runner = "1.6.2"
core = "1.6.1"
junit = "1.2.1"
datastore = "1.1.1"
koin = "4.1.0"
lifecycle = "2.9.1"
navigation = "2.9.0-beta04"

[libraries]
# Base
kotlin-coroutines = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-core", version.ref = "kotlin-coroutines" }
kotlin-coroutinesSlf4j = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-slf4j", version.ref = "kotlin-coroutines" }
kotlin-serialization = { module = "org.jetbrains.kotlinx:kotlinx-serialization-json", version.ref = "kotlin-serialization" }

# AndroidX Lifecycle & Navigation
lifecycle-viewmodel-compose = { group = "org.jetbrains.androidx.lifecycle", name = "lifecycle-viewmodel-compose", version.ref = "lifecycle" }
navigation-compose = { group = "org.jetbrains.androidx.navigation", name = "navigation-compose", version.ref = "navigation" }

# Logging
slf4j = { module = "org.slf4j:slf4j-api", version.ref = "slf4j" }
logback = { module = "ch.qos.logback:logback-classic", version.ref = "logback" }
janino = { module = "org.codehaus.janino:janino", version.ref = "janino" }
logstash-logback = { module = "net.logstash.logback:logstash-logback-encoder", version.ref = "logstash-logback" }
kotlin-logging = { module = "io.github.oshai:kotlin-logging", version.ref = "kotlin-logging" }
kmp-logging = { module = "co.touchlab:kermit", version = "2.0.4" }

# Config
config = { module = "com.typesafe:config", version = "1.4.3" }
config4k = { module = "io.github.config4k:config4k", version = "0.7.0" }

# gRPC and Protocol Buffers
protobuf-java = { module = "com.google.protobuf:protobuf-java", version.ref = "protobuf" }
protobuf-kotlin = { module = "com.google.protobuf:protobuf-kotlin", version.ref = "protobuf" }
grpc-protobuf = { module = "io.grpc:grpc-protobuf", version.ref = "grpc" }
grpc-stub = { module = "io.grpc:grpc-stub", version.ref = "grpc" }
grpc-nettyShaded = { module = "io.grpc:grpc-netty-shaded", version.ref = "grpc" }
grpc-services = { module = "io.grpc:grpc-services", version.ref = "grpc" }
grpc-kotlinStub = { module = "io.grpc:grpc-kotlin-stub", version.ref = "grpc-kotlin" }

# Ktor
ktor-clientCore = { module = "io.ktor:ktor-client-core", version.ref = "ktor" }
ktor-clientCio = { module = "io.ktor:ktor-client-cio", version.ref = "ktor" }
ktor-clientWebsockets = { module = "io.ktor:ktor-client-websockets", version.ref = "ktor" }
ktor-clientLogging = { module = "io.ktor:ktor-client-logging", version.ref = "ktor" }
ktor-clientContentNegotiation = { module = "io.ktor:ktor-client-content-negotiation", version.ref = "ktor" }
ktor-serializationKotlinxJson = { module = "io.ktor:ktor-serialization-kotlinx-json", version.ref = "ktor" }

# Koin
koin-core = { module = "io.insert-koin:koin-core", version.ref = "koin" }
koin-compose = { module = "io.insert-koin:koin-compose", version.ref = "koin" }
koin-composeVM = { module = "io.insert-koin:koin-compose-viewmodel", version.ref = "koin" }
koin-android = { module = "io.insert-koin:koin-android", version.ref = "koin" }

# Android
androidx-activity-compose = { module = "androidx.activity:activity-compose", version.ref = "androidx-activityCompose" }
grpc-okhttp-android = { module = "io.grpc:grpc-okhttp", version.ref = "grpc" }

# Compose
compose-ui = { module = "androidx.compose.ui:ui", version.ref = "compose" }
compose-ui-tooling = { module = "androidx.compose.ui:ui-tooling", version.ref = "compose" }
compose-ui-tooling-preview = { module = "androidx.compose.ui:ui-tooling-preview", version.ref = "compose" }
compose-foundation = { module = "androidx.compose.foundation:foundation", version.ref = "compose" }
compose-material3 = { module = "androidx.compose.material3:material3", version.ref = "compose-material3" }

# Android / Test
runner = { group = "androidx.test", name = "runner", version.ref = "runner" }
core = { group = "androidx.test", name = "core", version.ref = "core" }
junit = { group = "androidx.test.ext", name = "junit", version.ref = "junit" }

# Datastore
datastore = { module = "androidx.datastore:datastore", version.ref = "datastore" }
datastore-preferences = { module = "androidx.datastore:datastore-preferences", version.ref = "datastore" }

[plugins]
# Base
kotlinJvm = { id = "org.jetbrains.kotlin.jvm", version.ref = "kotlin" }
kotlin-serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin" }

# Android
android-application = { id = "com.android.application", version.ref = "agp" }
android-library = { id = "com.android.library", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }

# Compose
compose-compiler = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }

# iOS
kotlin-cocoapods = { id = "org.jetbrains.kotlin.native.cocoapods", version.ref = "kotlin" }

# gRPC and Protocol Buffers
protobuf = { id = "com.google.protobuf", version.ref = "protobuf-plugin" }
grpc-kmp = { id = "io.github.timortel.kmpgrpc.plugin", version.ref = "grpc-kmp" }

# Ktor
ktor = { id = "io.ktor.plugin", version.ref = "ktor" }

# Kotlin multiplatform
kotlin-multiplatform = { id = "org.jetbrains.kotlin.multiplatform", version.ref = "kotlin-version" }
android-kotlin-multiplatform-library = { id = "com.android.kotlin.multiplatform.library", version.ref = "agp" }
