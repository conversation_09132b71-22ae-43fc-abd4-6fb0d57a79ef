syntax = "proto3";

package app.closer;

import "google/protobuf/empty.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

option java_multiple_files = true;

service ChatService {
  rpc Login(LoginRequest) returns (LoginResponse);
  rpc ChatStream(stream ChatStreamRequest) returns (stream ChatStreamResponse);
  rpc GetCall(GetCallRequest) returns (GetCallResponse);
  rpc AnswerCall(AnswerCallRequest) returns (google.protobuf.Empty);
  rpc LeaveCall(LeaveCallRequest) returns (google.protobuf.Empty);
  rpc GetConversations(GetConversationsRequest) returns (GetConversationsResponse);
}

message LoginRequest {
  string email = 1;
  string password = 2;
}

message LoginResponse {
  string id = 1;
  string api_key = 2;
  string org_id = 3;
  string first_name = 4;
  string last_name = 5;
  string email = 6;
}

message RoomSendMessage {
  string room_id = 1;
  string body = 2;
  google.protobuf.Struct context = 3;
  string ref = 4;
}

message RoomSendCustomMessage {
  string room_id = 1;
  string body = 2;
  string subtag = 3;
  google.protobuf.Struct context = 4;
  string ref = 5;
}

message RoomSendTyping {
  string room_id = 1;
  string body = 2;
}

message RoomSendMark {
  string room_id = 1;
  google.protobuf.Timestamp timestamp = 2;
}

message RoomConfirmMessageDelivery {
  string room_id = 1;
  string event_id = 2;
  google.protobuf.Timestamp timestamp = 3;
}

message AudioStreamToggle {
  string call_id = 1;
  bool enabled = 2;
  google.protobuf.Timestamp timestamp = 3;
}

enum VideoType {
  VIDEO_TYPE_UNSPECIFIED = 0;
  VIDEO_TYPE_CAMERA = 1;
  VIDEO_TYPE_SCREEN = 2;
}

message VideoStreamToggle {
  string call_id = 1;
  bool enabled = 2;
  google.protobuf.Timestamp timestamp = 3;
  VideoType content = 4;
}

enum SDPType {
  SDP_TYPE_UNSPECIFIED = 0;
  SDP_TYPE_OFFER = 1;
  SDP_TYPE_ANSWER = 2;
}

message JSEP {
  SDPType type = 1;
  string sdp = 2;
}

message RtcSendDescription {
  string call_id = 1;
  string peer = 2;
  JSEP sdp = 3;
}

message IceCandidate {
  string candidate = 1;
  string sdp_mid = 2;
  int32 sdp_m_line_index = 3;
}

message RtcSendCandidate {
  string call_id = 1;
  string peer = 2;
  IceCandidate ice_candidate = 3;
}

message InputHeartbeat {
  google.protobuf.Timestamp timestamp = 1;
}

message ChatStreamRequest {
  oneof event {
    RoomSendMessage room_send_message = 1;
    RoomSendCustomMessage room_send_custom_message = 2;
    RoomSendTyping room_send_typing = 3;
    RoomSendMark room_send_mark = 4;
    RoomConfirmMessageDelivery room_confirm_message_delivery = 5;
    AudioStreamToggle audio_stream_toggle = 6;
    VideoStreamToggle video_stream_toggle = 7;
    RtcSendDescription rtc_send_description = 8;
    RtcSendCandidate rtc_send_candidate = 9;
    InputHeartbeat input_heartbeat = 10;
  }
}

message Hello {
  string device_id = 1;
  google.protobuf.Timestamp timestamp = 2;
  int64 heartbeat_timeout = 3;
  int64 reconnect_delay = 4;
}

message OutputHeartbeat {
  google.protobuf.Timestamp timestamp = 1;
}

message Disconnect {
  string device_id = 1;
}

message Unauthorized {}

message ErrorEvent {
  string reason = 1;
}

message RtcDescriptionSent {
  string call_id = 1;
  string sender = 2;
  JSEP sdp = 3;
}

message RtcCandidateSent {
  string call_id = 1;
  string sender = 2;
  IceCandidate candidate = 3;
}

message RoomCreated {
  string room_id = 1;
  string author_id = 2;
  google.protobuf.Timestamp timestamp = 3;
}

message RoomInvited {
  string room_id = 1;
  string author_id = 2;
  string invitee = 3;
  google.protobuf.Struct metadata = 4;
  google.protobuf.Timestamp timestamp = 5;
}

message RoomJoined {
  string room_id = 1;
  string author_id = 2;
  google.protobuf.Timestamp timestamp = 3;
}

enum EndReason {
  END_REASON_UNSPECIFIED = 0;
  END_REASON_TERMINATED = 1;
  END_REASON_TIMEOUT = 2;
  END_REASON_ENDED = 3;
  END_REASON_HANGUP = 4;
  END_REASON_CONNECTION_DROPPED = 5;
  END_REASON_DISCONNECTED = 6;
  END_REASON_REJECTED = 7;
  END_REASON_BUSY = 8;
  END_REASON_TIME_LIMIT_EXCEEDED = 9;
}

message RoomLeft {
  string room_id = 1;
  string author_id = 2;
  EndReason reason = 3;
  google.protobuf.Timestamp timestamp = 4;
}

message RoomMessageSent {
  string room_id = 1;
  string author_id = 2;
  string message = 3;
  string message_id = 4;
  google.protobuf.Struct context = 5;
  google.protobuf.Timestamp timestamp = 6;
}

message FakeRoomMessageSent {
  string room_id = 1;
  string author_id = 2;
  string message_id = 3;
  google.protobuf.Timestamp timestamp = 4;
}

message RoomCustomMessageSent {
  string room_id = 1;
  string author_id = 2;
  string message = 3;
  string message_id = 4;
  string subtag = 5;
  google.protobuf.Struct context = 6;
  google.protobuf.Timestamp timestamp = 7;
}

message RoomTypingSent {
  string room_id = 1;
  string author_id = 2;
  string preview = 3;
  google.protobuf.Timestamp timestamp = 4;
}

message RoomMarkSent {
  string room_id = 1;
  string author_id = 2;
  google.protobuf.Timestamp timestamp = 3;
}

message RoomMessageDelivered {
  string room_id = 1;
  string author_id = 2;
  string message_id = 3;
  google.protobuf.Timestamp timestamp = 4;
}

message RoomMessageUpdated {
  string room_id = 1;
  string author_id = 2;
  google.protobuf.Timestamp timestamp = 3;
  string message_id = 4;
  google.protobuf.Struct context = 5;
}

message ChatReceived {
  string event_id = 1;
  NormalizedEvent message = 2;
  string ref = 3;
}

message NormalizedEvent {
  string id = 1;
  string author_id = 2;
  string channel_id = 3;
  google.protobuf.Struct data = 4;
  google.protobuf.Timestamp timestamp = 5;
}

message CallAnswered {
  string call_id = 1;
  string author_id = 2;
  google.protobuf.Struct metadata = 3;
  google.protobuf.Timestamp timestamp = 4;
}

message AudioStreamToggled {
  string call_id = 1;
  string user_id = 2;
  bool enabled = 3;
  google.protobuf.Timestamp timestamp = 4;
}

message CallHandledOnDevice {
  string call_id = 1;
  string author_id = 2;
  string device = 3;
  google.protobuf.Timestamp timestamp = 4;
}

message CallCreated {
  string call_id = 1;
  string author_id = 2;
  google.protobuf.Timestamp timestamp = 3;
}

message DeviceOffline {
  string call_id = 1;
  string user_id = 2;
  string device_id = 3;
  google.protobuf.Timestamp timestamp = 4;
}

message DeviceOnline {
  string call_id = 1;
  string user_id = 2;
  string device_id = 3;
  google.protobuf.Timestamp timestamp = 4;
}

message CallEnded {
  string call_id = 1;
  EndReason reason = 2;
  google.protobuf.Timestamp timestamp = 3;
}

message CallInvited {
  string call_id = 1;
  string author_id = 2;
  string invitee = 3;
  google.protobuf.Struct metadata = 4;
  google.protobuf.Timestamp timestamp = 5;
}

message CallJoined {
  string call_id = 1;
  string author_id = 2;
  google.protobuf.Timestamp timestamp = 3;
}

message CallLeft {
  string call_id = 1;
  string author_id = 2;
  EndReason reason = 3;
  google.protobuf.Timestamp timestamp = 4;
}

message CallRejected {
  string call_id = 1;
  string author_id = 2;
  EndReason reason = 3;
  google.protobuf.Timestamp timestamp = 4;
}

message VideoStreamToggled {
  string call_id = 1;
  string user_id = 2;
  bool enabled = 3;
  google.protobuf.Timestamp timestamp = 4;
  VideoType content = 5;
}

// ExternalEvent messages
message GuestProfileUpdated {
  string room_id = 1;
  string customer_id = 2;
  string first_name = 3;
  string last_name = 4;
  string email = 5;
  Phone phone = 6;
  string locale = 7;
  string zone_id = 8;
  repeated BackofficeField back_office_data = 9;
  repeated string tags = 10;
  string tag_group_id = 11;
  repeated string topics = 12;
}

message Phone {
  string region = 1;
  string number = 2;
}

message BackofficeField {
  string key = 1;
  google.protobuf.Value value = 2;
  string display_name = 3;
}

message LastAdviserTimestampRemoved {
  string room_id = 1;
}

message LastAdviserTimestampSet {
  string room_id = 1;
  google.protobuf.Timestamp timestamp = 2;
}

message ConversationSnoozed {
  string room_id = 1;
}

message ConversationUnsnoozed {
  string room_id = 1;
}

message NotificationUpcomingMeeting {
  string meeting_id = 1;
  string room_id = 2;
  google.protobuf.Timestamp start = 3;
  int64 duration = 4;
  int64 minutes_to_meeting = 5;
  string guest_id = 6;
  string guest_name = 7;
  string lang_tag = 8;
}

enum Presence {
  PRESENCE_UNSPECIFIED = 0;
  PRESENCE_AVAILABLE = 1;
  PRESENCE_UNAVAILABLE = 2;
}

message AgentStatusUpdated {
  string agent_id = 1;
  AvailableSubstatus available = 2;
  UnreadySubstatus unready = 3;
  string unavailable = 4;
  Presence presence = 5;
  bool registered_for_push = 6;
  google.protobuf.Timestamp timestamp = 7;
}

enum AvailableSubstatus {
  AVAILABLE_SUBSTATUS_UNSPECIFIED = 0;
  AVAILABLE_SUBSTATUS_CHAT_AND_CALL = 1;
  AVAILABLE_SUBSTATUS_CHAT_ONLY = 2;
}

enum UnreadySubstatus {
  UNREADY_SUBSTATUS_UNSPECIFIED = 0;
  UNREADY_SUBSTATUS_PREPARING = 1;
  UNREADY_SUBSTATUS_COOL_DOWN = 2;
  UNREADY_SUBSTATUS_ON_CALL = 3;
  UNREADY_SUBSTATUS_AFTER_CALL = 4;
}

message AssigneeStatusUpdated {
  string agent_id = 1;
  bool are_calls_possible = 2;
  google.protobuf.Timestamp timestamp = 3;
}

message LeadPresenceUpdated {
  string lead_id = 1;
  Presence presence = 2;
  google.protobuf.Timestamp timestamp = 3;
}

message UnreadCountUpdated {
  string room_id = 1;
  int32 unread_count = 2;
}

message UnreadTotalUpdated {
  ConversationTab tab = 1;
  int32 unread_count = 2;
  int32 unread_count_no_tag_group = 3;
  map<string, int32> unread_count_by_tag_group = 4;
}

enum ConversationTab {
  CONVERSATION_TAB_UNSPECIFIED = 0;
  CONVERSATION_TAB_WAITING = 1;
  CONVERSATION_TAB_YOURS = 2;
  CONVERSATION_TAB_FOLLOWED = 3;
  CONVERSATION_TAB_IN_PROGRESS = 4;
  CONVERSATION_TAB_CLOSED = 5;
}

message UnassignedCountUpdated {
  int32 count = 1;
  int32 count_no_tag_group = 2;
  map<string, int32> count_by_tag_group = 3;
}

message ConversationStatusChanged {
  string room_id = 1;
  ConversationStatus status = 2;
  string adviser_id = 3;
  google.protobuf.Timestamp timestamp = 4;
}

enum ConversationStatus {
  CONVERSATION_STATUS_UNSPECIFIED = 0;
  CONVERSATION_STATUS_NEW = 1;
  CONVERSATION_STATUS_WAITING = 2;
  CONVERSATION_STATUS_IN_PROGRESS = 3;
  CONVERSATION_STATUS_SOLVED = 4;
  CONVERSATION_STATUS_UNSOLVED = 5;
}

message FollowerAdded {
  string room_id = 1;
  string adviser_id = 2;
  string requester_id = 3;
}

message FollowerRemoved {
  string room_id = 1;
  string adviser_id = 2;
  string requester_id = 3;
}

message MeetingScheduled {
  string room_id = 1;
  string meeting_id = 2;
  string adviser_id = 3;
  google.protobuf.Timestamp start = 4;
  int64 duration = 5;
}

message MeetingRescheduled {
  string room_id = 1;
  string meeting_id = 2;
  string adviser_id = 3;
  google.protobuf.Timestamp start = 4;
  int64 duration = 5;
}

message MeetingCancelled {
  string room_id = 1;
  string meeting_id = 2;
  string adviser_id = 3;
  google.protobuf.Timestamp start = 4;
  int64 duration = 5;
}

message AssigneeChanged {
  string room_id = 1;
  string adviser_id = 2;
  string requester_id = 3;
}

message AssigneeRemoved {
  string room_id = 1;
  string adviser_id = 2;
}

message AllFollowersRemoved {
  string room_id = 1;
}

message CallUnsuccessful {
  string room_id = 1;
}

message AgentGroupCreated {
  string id = 1;
  string name = 2;
  repeated string tags = 3;
  repeated string tag_groups = 4;
  repeated string advisers = 5;
}

message AgentGroupUpdated {
  string id = 1;
  string name = 2;
  repeated string tags = 3;
  repeated string tag_groups = 4;
  repeated string advisers = 5;
}

message AdviserTagGroupsUpdated {
  string id = 1;
  repeated string tag_groups = 2;
}

message AgentGroupDeleted {
  string id = 1;
}

message ChatStreamResponse {
  oneof event {
    Hello hello = 1;
    OutputHeartbeat output_heartbeat = 2;
    Disconnect disconnect = 3;
    Unauthorized unauthorized = 4;
    ErrorEvent error = 5;
    RtcDescriptionSent rtc_description_sent = 6;
    RtcCandidateSent rtc_candidate_sent = 7;
    RoomCreated room_created = 8;
    RoomInvited room_invited = 9;
    RoomJoined room_joined = 10;
    RoomLeft room_left = 11;
    RoomMessageSent room_message_sent = 12;
    FakeRoomMessageSent fake_room_message_sent = 13;
    RoomCustomMessageSent room_custom_message_sent = 14;
    RoomTypingSent room_typing_sent = 15;
    RoomMarkSent room_mark_sent = 16;
    RoomMessageDelivered room_message_delivered = 17;
    RoomMessageUpdated room_message_updated = 18;
    ChatReceived chat_received = 19;
    CallAnswered call_answered = 20;
    AudioStreamToggled audio_stream_toggled = 21;
    CallHandledOnDevice call_handled_on_device = 22;
    CallCreated call_created = 23;
    DeviceOffline device_offline = 24;
    DeviceOnline device_online = 25;
    CallEnded call_ended = 26;
    CallInvited call_invited = 27;
    CallJoined call_joined = 28;
    CallLeft call_left = 29;
    CallRejected call_rejected = 30;
    VideoStreamToggled video_stream_toggled = 31;
    GuestProfileUpdated guest_profile_updated = 32;
    LastAdviserTimestampRemoved last_adviser_timestamp_removed = 33;
    LastAdviserTimestampSet last_adviser_timestamp_set = 34;
    ConversationSnoozed conversation_snoozed = 35;
    ConversationUnsnoozed conversation_unsnoozed = 36;
    NotificationUpcomingMeeting notification_upcoming_meeting = 37;
    AgentStatusUpdated agent_status_updated = 38;
    AssigneeStatusUpdated assignee_status_updated = 39;
    LeadPresenceUpdated lead_presence_updated = 40;
    UnreadCountUpdated unread_count_updated = 41;
    UnreadTotalUpdated unread_total_updated = 42;
    UnassignedCountUpdated unassigned_count_updated = 43;
    ConversationStatusChanged conversation_status_changed = 44;
    FollowerAdded follower_added = 45;
    FollowerRemoved follower_removed = 46;
    MeetingScheduled meeting_scheduled = 47;
    MeetingRescheduled meeting_rescheduled = 48;
    MeetingCancelled meeting_cancelled = 49;
    AssigneeChanged assignee_changed = 50;
    AssigneeRemoved assignee_removed = 51;
    AllFollowersRemoved all_followers_removed = 52;
    CallUnsuccessful call_unsuccessful = 53;
    AgentGroupCreated agent_group_created = 54;
    AgentGroupUpdated agent_group_updated = 55;
    AdviserTagGroupsUpdated adviser_tag_groups_updated = 56;
    AgentGroupDeleted agent_group_deleted = 57;
  }
}

message GetCallRequest {
  string call_id = 1;
}

message GetCallResponse {
  string id = 1;
  google.protobuf.Timestamp created = 2;
  google.protobuf.Timestamp ended = 3;
  string creator = 4;
  repeated string users = 5;
  repeated string invitees = 6;
  bool direct = 7;
  string org_id = 8;
  bool recording_enabled = 9;
}

message AnswerCallRequest {
  string call_id = 1;
  google.protobuf.Struct metadata = 2;
}

message LeaveCallRequest {
  string call_id = 1;
  EndReason reason = 2;
}

enum InboxSorting {
  INBOX_SORTING_UNSPECIFIED = 0;
  INBOX_SORTING_NEWEST = 1;
  INBOX_SORTING_OLDEST = 2;
}

message GetConversationsRequest {
  ConversationTab tab = 1;
  int32 limit = 2;
  int64 offset = 3;
  repeated string tag = 4;
  bool only_snoozed = 5;
  string assignee_id = 6;
  int64 any_thread_closed_after = 7;
  int64 any_thread_closed_before = 8;
  InboxSorting sorting = 9;
  repeated string tag_group_id = 10;
  bool closed = 11;
}

message InboxEntryProfile {
  string first_name = 1;
  string last_name = 2;
  string email = 3;
  Phone phone = 4;
  string random_name = 5;
  string searchable_field = 6;
}

enum InboxEntryType {
  INBOX_ENTRY_TYPE_UNSPECIFIED = 0;
  INBOX_ENTRY_TYPE_MESSAGE = 1;
  INBOX_ENTRY_TYPE_NOTE = 2;
  INBOX_ENTRY_TYPE_FILES = 3;
  INBOX_ENTRY_TYPE_MEETING_SCHEDULED = 4;
  INBOX_ENTRY_TYPE_MEETING_RESCHEDULED = 5;
  INBOX_ENTRY_TYPE_MEETING_CANCELLED = 6;
  INBOX_ENTRY_TYPE_CALL_STARTED = 7;
  INBOX_ENTRY_TYPE_CALL_ENDED = 8;
  INBOX_ENTRY_TYPE_FORM = 9;
}

enum SnoozedStatus {
  SNOOZED_STATUS_UNSPECIFIED = 0;
  SNOOZED_STATUS_SNOOZED = 1;
  SNOOZED_STATUS_UNSNOOZED = 2;
}

message InboxWithLeadProfileEntry {
  string room = 1;
  string owner = 2;
  string sender = 3;
  InboxEntryProfile profile = 4;
  ConversationStatus status = 5;
  string assignee = 6;
  InboxEntryType type = 7;
  string message = 8;
  int64 timestamp = 9;
  int64 last_guest_timestamp = 10;
  int64 last_adviser_timestamp = 11;
  int64 thread_created_at = 12;
  int64 thread_closed_at = 13;
  int64 mark = 14;
  int32 unread_count = 15;
  Presence owner_presence = 16;
  repeated string tags = 17;
  bool is_snoozed = 18;
  google.protobuf.Struct payload = 19;
  SnoozedStatus snoozed_status = 20;
  string tag_group_id = 21;
}

message GetConversationsResponse {
  repeated InboxWithLeadProfileEntry entries = 1;
  int64 timestamp = 2;
}
