plugins {
    alias(libs.plugins.kotlinJvm)
    alias(libs.plugins.kotlin.serialization)
    alias(libs.plugins.protobuf)
    alias(libs.plugins.ktor)
    application
}

kotlin {
    compilerOptions {
        optIn.add("kotlin.time.ExperimentalTime")
        optIn.add("kotlin.uuid.ExperimentalUuidApi")
        optIn.add("kotlinx.serialization.ExperimentalSerializationApi")
    }
}

application {
    mainClass.set("app.closer.mobile_sdk.ApplicationKt")
}

dependencies {
    implementation(libs.kotlin.coroutines)
    implementation(libs.kotlin.coroutinesSlf4j)
    implementation(libs.kotlin.serialization)

    implementation(libs.slf4j)
    implementation(libs.logback)
    implementation(libs.janino)
    implementation(libs.logstash.logback)
    implementation(libs.kotlin.logging)

    implementation(libs.config)
    implementation(libs.config4k)

    implementation(libs.protobuf.java)
    implementation(libs.protobuf.kotlin)
    implementation(libs.grpc.protobuf)
    implementation(libs.grpc.stub)
    implementation(libs.grpc.nettyShaded)
    implementation(libs.grpc.services)
    implementation(libs.grpc.kotlinStub)

    implementation(libs.ktor.clientCore)
    implementation(libs.ktor.clientCio)
    implementation(libs.ktor.clientWebsockets)
    implementation(libs.ktor.clientLogging)
    implementation(libs.ktor.clientContentNegotiation)
    implementation(libs.ktor.serializationKotlinxJson)
}

sourceSets {
    main {
        proto {
            srcDirs("../protos/src/main/proto")
        }
    }
}

protobuf {
    protoc {
        artifact = "com.google.protobuf:protoc:${libs.versions.protobuf.asProvider().get()}"
    }

    plugins {
        create("grpc") {
            artifact = "io.grpc:protoc-gen-grpc-java:${libs.versions.grpc.asProvider().get()}"
        }
        create("grpckt") {
            artifact = "io.grpc:protoc-gen-grpc-kotlin:${libs.versions.grpc.kotlin.get()}:jdk8@jar"
        }
    }
    generateProtoTasks {
        ofSourceSet("main").forEach {
            it.plugins {
                create("grpc") { }
                create("grpckt") { }
            }

            it.builtins {
                create("kotlin") {
                    option("lite")
                }
            }
        }
    }
}

tasks {
    distTar {
        archiveBaseName.set("sdk")
        archiveClassifier.set("")
        archiveVersion.set("")
    }
}
