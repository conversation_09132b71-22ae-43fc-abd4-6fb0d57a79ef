package app.closer.mobile_sdk

import app.closer.mobile_sdk.artichoke.ArtichokeClient
import app.closer.mobile_sdk.auth.ContextInterceptor
import app.closer.mobile_sdk.spinner.SpinnerClient
import io.github.oshai.kotlinlogging.KotlinLogging
import io.grpc.ServerBuilder
import io.grpc.ServerInterceptors
import io.grpc.health.v1.HealthCheckResponse
import io.grpc.protobuf.services.HealthStatusManager

private val logger = KotlinLogging.logger {}

fun main() {
    val healthService = HealthStatusManager()
        .apply { setStatus("", HealthCheckResponse.ServingStatus.SERVING) }
        .healthService
    val artichokeClient = ArtichokeClient(GlobalConfig.artichoke.host)
    val spinnerClient = SpinnerClient(GlobalConfig.spinner.host)
    val chatService = ChatService(artichokeClient, spinnerClient)
    val interceptor = ContextInterceptor()

    ServerBuilder.forPort(GlobalConfig.http.server.port)
        .addService(healthService)
        .addService(ServerInterceptors.intercept(chatService, interceptor))
        .build()
        .start()
        .also { logger.info { "gRPC server started on port: ${it.port}" } }
        .awaitTermination()
}
