package app.closer.mobile_sdk

import app.closer.*
import app.closer.mobile_sdk.artichoke.ArtichokeClient
import app.closer.mobile_sdk.artichoke.model.CallId
import app.closer.mobile_sdk.artichoke.model.conversions.toChatStreamResponse
import app.closer.mobile_sdk.artichoke.model.conversions.toDomain
import app.closer.mobile_sdk.artichoke.model.conversions.toDomainCommand
import app.closer.mobile_sdk.artichoke.model.conversions.toGetCallResponse
import app.closer.mobile_sdk.auth.ApiKey
import app.closer.mobile_sdk.auth.DeviceId
import app.closer.mobile_sdk.conversions.toJsonObject
import app.closer.mobile_sdk.spinner.SpinnerClient
import app.closer.mobile_sdk.spinner.model.conversions.toDomain
import app.closer.mobile_sdk.spinner.model.conversions.toGetConversationsResponse
import app.closer.mobile_sdk.spinner.model.conversions.toLoginResponse
import com.google.protobuf.Empty
import com.google.protobuf.TextFormat
import io.github.oshai.kotlinlogging.KotlinLogging
import io.grpc.Status
import io.grpc.StatusException
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.mapNotNull
import kotlinx.coroutines.flow.onEach
import kotlin.coroutines.coroutineContext
import kotlin.uuid.Uuid
import app.closer.GetConversationsRequest as ProtoGetConversationsRequest

private val logger = KotlinLogging.logger {}

class ChatService(
    private val artichokeClient: ArtichokeClient,
    private val spinnerClient: SpinnerClient
) : ChatServiceGrpcKt.ChatServiceCoroutineImplBase() {

    override suspend fun login(request: LoginRequest): LoginResponse {
        logger.info { "Login received email: ${request.email}" }

        try {
            val adviserDto = spinnerClient.login(request.email, request.password)
            logger.info { "Login completed successfully" }
            return adviserDto.toLoginResponse()
        } catch (e: StatusException) {
            throw e
        } catch (e: Exception) {
            logger.error(e) { "Login request failed with: ${e.message}" }
            throw StatusException(Status.INTERNAL)
        }
    }

    override fun chatStream(requests: Flow<ChatStreamRequest>): Flow<ChatStreamResponse> = flow {
        val apiKey = coroutineContext.getOrFail(ApiKey.Key)
        val deviceId = coroutineContext[DeviceId.Key]

        requests
            .onEach { logger.info { "ChatStreamRequest: ${printer.printToString(it)} + apiKey: $apiKey" } }
            .mapNotNull { it.toDomainCommand() }
            .onEach { logger.info { "DomainCommand: $it" } }
            .flowOn(coroutineContext.withoutJob())
            .let { artichokeClient.connect(apiKey, deviceId, it) }
            .onEach { logger.info { "DomainEvent: $it" } }
            .mapNotNull { it.toChatStreamResponse() }
            .onEach { logger.info { "ChatStreamResponse: ${printer.printToString(it)}" } }
            .let { emitAll(it) }
    }

    override suspend fun getCall(request: GetCallRequest): GetCallResponse {
        val apiKey = coroutineContext.getOrFail(ApiKey.Key)

        logger.info { "GetCall received: ${printer.printToString(request)} + apiKey: $apiKey" }

        try {
            val callDto = artichokeClient.getCall(callId = CallId(Uuid.parse(request.callId)))
            logger.info { "GetCall completed successfully" }
            return callDto.toGetCallResponse()
        } catch (e: StatusException) {
            throw e
        } catch (e: Exception) {
            logger.error(e) { "GetCall request failed with: ${e.message}" }
            throw StatusException(Status.INTERNAL)
        }
    }

    override suspend fun answerCall(request: AnswerCallRequest): Empty {
        val apiKey = coroutineContext.getOrFail(ApiKey.Key)
        val deviceId = coroutineContext.getOrFail(DeviceId.Key)

        logger.info { "AnswerCall received: ${printer.printToString(request)} (apiKey: $apiKey, deviceId: $deviceId)" }

        try {
            artichokeClient.answerCall(
                apiKey = apiKey,
                deviceId = deviceId,
                callId = CallId(Uuid.parse(request.callId)),
                metadata = request.metadata.toJsonObject().takeIf { it.size != 0 },
            )
            return Empty.getDefaultInstance()
        } catch (e: StatusException) {
            throw e
        } catch (e: Exception) {
            logger.error(e) { "AnswerCall request failed with: ${e.message}" }
            throw StatusException(Status.INTERNAL)
        }
    }

    override suspend fun leaveCall(request: LeaveCallRequest): Empty {
        val apiKey = coroutineContext.getOrFail(ApiKey.Key)
        val deviceId = coroutineContext.getOrFail(DeviceId.Key)
        val reason = request.reason.toDomain() ?: throw StatusException(Status.INVALID_ARGUMENT)

        logger.info { "LeaveCall received: ${printer.printToString(request)} (apiKey: $apiKey, deviceId: $deviceId)" }

        try {
            artichokeClient.leaveCall(
                apiKey = apiKey,
                deviceId = deviceId,
                callId = CallId(Uuid.parse(request.callId)),
                reason = reason
            )
            return Empty.getDefaultInstance()
        } catch (e: StatusException) {
            throw e
        } catch (e: Exception) {
            logger.error(e) { "LeaveCall request failed with: ${e.message}" }
            throw StatusException(Status.INTERNAL)
        }
    }

    override suspend fun getConversations(request: ProtoGetConversationsRequest): GetConversationsResponse {
        val apiKey = coroutineContext.getOrFail(ApiKey.Key)

        logger.info { "GetConversations received: limit=${request.limit} (apiKey: $apiKey)" }

        try {
            val domainRequest = request.toDomain()
            val inboxState = spinnerClient.getConversations(
                request = domainRequest,
                apiKey = apiKey.value
            )
            logger.info { "GetConversations completed successfully" }
            return inboxState.toGetConversationsResponse()
        } catch (e: StatusException) {
            throw e
        } catch (e: Exception) {
            logger.error(e) { "GetConversations request failed with: ${e.message}" }
            throw StatusException(Status.INTERNAL)
        }
    }

    companion object {
        private val printer = TextFormat.printer().emittingSingleLine(true)
    }
}
