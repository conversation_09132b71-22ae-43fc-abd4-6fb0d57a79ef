package app.closer.mobile_sdk

import io.grpc.Status
import io.grpc.StatusException
import kotlinx.coroutines.Job
import kotlin.coroutines.CoroutineContext
import kotlin.coroutines.CoroutineContext.Element
import kotlin.coroutines.CoroutineContext.Key

fun CoroutineContext.withoutJob(): CoroutineContext =
    minusKey(Job.Key)

fun <E : Element> CoroutineContext.getOrFail(key: Key<E>): E =
    this[key] ?: throw StatusException(Status.UNAUTHENTICATED)
