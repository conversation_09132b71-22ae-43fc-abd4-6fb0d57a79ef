package app.closer.mobile_sdk

import com.typesafe.config.ConfigFactory
import io.github.config4k.extract

data class Root(val http: Http, val artichoke: Artichoke, val spinner: Spinner) {
    companion object Companion {
        data class Http(val server: Server) {
            companion object Companion {
                data class Server(val port: Int)
            }
        }

        data class Artichoke(val host: String)
        data class Spinner(val host: String)
    }
}

val GlobalConfig: Root by lazy { ConfigFactory.load().extract() }
