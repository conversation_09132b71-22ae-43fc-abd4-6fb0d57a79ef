package app.closer.mobile_sdk

import io.grpc.Status
import io.ktor.http.*


fun HttpStatusCode.toGrpcStatus(): Status =
    when (this) {
        HttpStatusCode.BadRequest -> Status.INVALID_ARGUMENT
        HttpStatusCode.Unauthorized -> Status.UNAUTHENTICATED
        HttpStatusCode.Forbidden -> Status.PERMISSION_DENIED
        HttpStatusCode.NotFound, HttpStatusCode.Gone -> Status.NOT_FOUND
        HttpStatusCode.Conflict -> Status.ABORTED
        HttpStatusCode.TooManyRequests -> Status.RESOURCE_EXHAUSTED
        HttpStatusCode.NotImplemented -> Status.UNIMPLEMENTED
        HttpStatusCode.BadGateway, HttpStatusCode.ServiceUnavailable, HttpStatusCode.GatewayTimeout -> Status.UNAVAILABLE
        HttpStatusCode.InternalServerError -> Status.INTERNAL
        else -> Status.UNKNOWN.withDescription("HTTP $value: $description")
    }
