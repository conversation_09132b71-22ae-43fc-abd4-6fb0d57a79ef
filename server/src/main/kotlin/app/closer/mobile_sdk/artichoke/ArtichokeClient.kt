package app.closer.mobile_sdk.artichoke

import app.closer.mobile_sdk.artichoke.model.*
import app.closer.mobile_sdk.auth.ApiKey
import app.closer.mobile_sdk.auth.DeviceId
import app.closer.mobile_sdk.toGrpcStatus
import io.github.oshai.kotlinlogging.KotlinLogging
import io.grpc.StatusException
import io.ktor.client.*
import io.ktor.client.call.*
import io.ktor.client.engine.cio.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.client.plugins.logging.*
import io.ktor.client.plugins.websocket.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.serialization.kotlinx.json.*
import io.ktor.websocket.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.channelFlow
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.launch
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonObject
import kotlin.time.Duration.Companion.seconds

private val logger = KotlinLogging.logger {}

class ArtichokeClient(private val serverUrl: String) {

    private val client = HttpClient(CIO) {
        install(WebSockets) {
            pingInterval = 20.seconds
            maxFrameSize = Long.MAX_VALUE
        }
        install(Logging) {
            level = LogLevel.INFO
        }
        install(ContentNegotiation) {
            json(DefaultJson)
        }
    }

    suspend fun getCall(callId: CallId): CallDto {
        logger.info { "Calling getCall with callId: $callId" }

        val response = client.get("https://$serverUrl/api/calls/${callId.value}")

        if (!response.status.isSuccess()) {
            val errorBody = response.bodyAsText()
            logger.error { "getCall failed with status: ${response.status}, body: $errorBody" }
            throw StatusException(response.status.toGrpcStatus())
        }

        val callDto = response.body<CallDto>()
        logger.info { "getCall returned: $callDto" }
        return callDto
    }

    suspend fun answerCall(apiKey: ApiKey, deviceId: DeviceId, callId: CallId, metadata: JsonObject?) {
        logger.info { "Calling answerCall with callId: $callId, metadata: $metadata" }

        val response = client.post("https://$serverUrl/api/calls/${callId.value}/answer") {
            header("X-Api-Key", apiKey.value)
            header("X-Device-Id", deviceId.value)
            contentType(ContentType.Application.Json)
            setBody(AnswerCallForm(metadata))
        }

        if (response.status != HttpStatusCode.NoContent) {
            val errorBody = response.bodyAsText()
            logger.error { "answerCall failed with status: ${response.status}, body: $errorBody" }
            throw StatusException(response.status.toGrpcStatus())
        }

        logger.info { "answerCall completed successfully" }
    }

    suspend fun leaveCall(apiKey: ApiKey, deviceId: DeviceId, callId: CallId, reason: EndReason) {
        logger.info { "Calling leaveCall with callId: $callId, reason: $reason" }

        val response = client.post("https://$serverUrl/api/calls/${callId.value}/leave") {
            header("X-Api-Key", apiKey.value)
            header("X-Device-Id", deviceId.value)
            contentType(ContentType.Application.Json)
            setBody(LeaveCallForm(reason))
        }

        if (response.status != HttpStatusCode.NoContent) {
            val errorBody = response.bodyAsText()
            logger.error { "leaveCall failed with status: ${response.status}, body: $errorBody" }
            throw StatusException(response.status.toGrpcStatus())
        }

        logger.info { "leaveCall completed successfully" }
    }

    fun connect(
        apiKey: ApiKey,
        deviceId: DeviceId?,
        inputMessages: Flow<DomainCommand>
    ): Flow<DomainEvent> = channelFlow {
        logger.info { "Starting WebSocket session" }

        try {
            client.webSocket(
                host = serverUrl,
                path = "/ws/${apiKey.value}" + (deviceId?.let { "/reconnect/${it.value}" } ?: "")
            ) {
                launch {
                    inputMessages
                        .onCompletion { it?.let { closeExceptionally(it) } ?: close() }
                        .collect { outgoing.send(Frame.Text(DefaultJson.encodeToString(it))) }
                }

                for (frame in incoming) {
                    when (frame) {
                        is Frame.Text ->
                            send(DefaultJson.decodeFromString<DomainEvent>(frame.readText()))

                        is Frame.Binary ->
                            send(DefaultJson.decodeFromString<DomainEvent>(frame.data.toString(Charsets.UTF_8)))

                        is Frame.Ping ->
                            logger.debug { "Received Ping frame" }

                        is Frame.Pong ->
                            logger.debug { "Received Pong frame" }

                        is Frame.Close -> {
                            logger.info { "Received Close frame with reason: ${frame.readReason()}, closing session" }
                            break
                        }
                    }
                }
            }
        } catch (e: Exception) {
            logger.error(e) { "Error in WebSocket session" }
            close(e)
        } finally {
            logger.info { "WebSocket session closed" }
        }
    }

    companion object {
        val DefaultJson = Json {
            explicitNulls = false
            ignoreUnknownKeys = true
        }
    }
}
