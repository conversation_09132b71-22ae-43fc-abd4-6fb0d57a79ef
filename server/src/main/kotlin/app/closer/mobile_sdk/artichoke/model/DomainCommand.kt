package app.closer.mobile_sdk.artichoke.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonClassDiscriminator
import kotlinx.serialization.json.JsonObject

@Serializable
@JsonClassDiscriminator("tag")
sealed interface DomainCommand {
    companion object {
        sealed interface Room : DomainCommand {
            val roomId: RoomId

            companion object Companion {
                @Serializable
                @SerialName("room_send_message")
                data class RoomSendMessage(
                    override val roomId: RoomId,
                    val body: String,
                    val context: JsonObject,
                    val ref: String?
                ) : Room

                @Serializable
                @SerialName("room_send_custom_message")
                data class RoomSendCustomMessage(
                    override val roomId: RoomId,
                    val body: String,
                    val subtag: String,
                    val context: JsonObject,
                    val ref: String?
                ) : Room

                @Serializable
                @SerialName("room_send_typing")
                data class RoomSendTyping(
                    override val roomId: RoomId,
                    val body: String?
                ) : Room

                @Serializable
                @SerialName("room_send_mark")
                data class RoomSendMark(
                    override val roomId: RoomId,
                    val timestamp: Timestamp
                ) : Room

                @Serializable
                @SerialName("room_confirm_message_delivery")
                data class RoomConfirmMessageDelivery(
                    override val roomId: RoomId,
                    val eventId: NormalizedEventId,
                    val timestamp: Timestamp
                ) : Room
            }
        }

        sealed interface Call : DomainCommand {
            val callId: CallId

            companion object Companion {
                @Serializable
                @SerialName("audio_stream_toggle")
                data class AudioStreamToggle(
                    override val callId: CallId,
                    val enabled: Boolean,
                    val timestamp: Timestamp
                ) : Call

                @Serializable
                @SerialName("video_stream_toggle")
                data class VideoStreamToggle(
                    override val callId: CallId,
                    val enabled: Boolean,
                    val timestamp: Timestamp,
                    val content: VideoType?
                ) : Call {
                    companion object Companion {
                        enum class VideoType {
                            @SerialName("camera")
                            CAMERA,

                            @SerialName("screen")
                            SCREEN
                        }
                    }
                }
            }
        }

        sealed interface RTCSignalling : DomainCommand {
            val callId: CallId
            val peer: SessionId

            companion object Companion {
                @Serializable
                @SerialName("rtc_send_description")
                data class RtcSendDescription(
                    override val callId: CallId,
                    override val peer: SessionId,
                    val sdp: JSEP
                ) : RTCSignalling

                @Serializable
                @SerialName("rtc_send_candidate")
                data class RtcSendCandidate(
                    override val callId: CallId,
                    override val peer: SessionId,
                    val iceCandidate: IceCandidate?
                ) : RTCSignalling {
                    companion object Companion {
                        @Serializable
                        data class IceCandidate(
                            val candidate: String,
                            val sdpMid: String?,
                            val sdpMLineIndex: Int?
                        )
                    }
                }
            }
        }

        @Serializable
        @SerialName("input_heartbeat")
        data class InputHeartbeat(
            val timestamp: Timestamp
        ) : DomainCommand
    }
}
