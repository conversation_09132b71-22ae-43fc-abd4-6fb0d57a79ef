package app.closer.mobile_sdk.artichoke.model

import app.closer.mobile_sdk.auth.DeviceId
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonClassDiscriminator
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonObject
import java.time.ZoneId

@Serializable
@JsonClassDiscriminator("tag")
sealed interface DomainEvent {
    companion object {
        @Serializable
        @SerialName("hello")
        data class Hello(
            val deviceId: DeviceId,
            val timestamp: Timestamp,
            val heartbeatTimeout: Long,
            val reconnectDelay: Long
        ) : DomainEvent

        @Serializable
        @SerialName("output_heartbeat")
        data class OutputHeartbeat(
            val timestamp: Timestamp
        ) : DomainEvent

        @Serializable
        @SerialName("disconnect")
        data class Disconnect(
            val deviceId: DeviceId
        ) : DomainEvent

        @Serializable
        @SerialName("unauthorized")
        data object Unauthorized : DomainEvent

        sealed interface ErrorEvent : DomainEvent {
            companion object {
                @Serializable
                @SerialName("error")
                data class Error(
                    val reason: String
                ) : ErrorEvent
            }
        }

        sealed interface RTCSignallingEvent : DomainEvent {
            val callId: CallId
            val sender: SessionId

            companion object {
                @Serializable
                @SerialName("rtc_description_sent")
                data class RtcDescriptionSent(
                    override val callId: CallId,
                    override val sender: SessionId,
                    val sdp: JSEP
                ) : RTCSignallingEvent

                @Serializable
                @SerialName("rtc_candidate_sent")
                data class RtcCandidateSent(
                    override val callId: CallId,
                    override val sender: SessionId,
                    val candidate: IceCandidate?
                ) : RTCSignallingEvent {
                    companion object {
                        @Serializable
                        data class IceCandidate(
                            val candidate: String,
                            val sdpMid: String?,
                            val sdpMLineIndex: Int?
                        )
                    }
                }
            }
        }

        sealed interface RoomEvent : DomainEvent {
            val roomId: RoomId
            val authorId: SessionId
            val timestamp: Timestamp

            companion object {
                @Serializable
                @SerialName("room_created")
                data class RoomCreated(
                    override val roomId: RoomId,
                    override val authorId: SessionId,
                    override val timestamp: Timestamp
                ) : RoomEvent

                @Serializable
                @SerialName("room_invited")
                data class RoomInvited(
                    override val roomId: RoomId,
                    override val authorId: SessionId,
                    val invitee: SessionId,
                    val metadata: JsonObject?,
                    override val timestamp: Timestamp
                ) : RoomEvent

                @Serializable
                @SerialName("room_joined")
                data class RoomJoined(
                    override val roomId: RoomId,
                    override val authorId: SessionId,
                    override val timestamp: Timestamp
                ) : RoomEvent

                @Serializable
                @SerialName("room_left")
                data class RoomLeft(
                    override val roomId: RoomId,
                    override val authorId: SessionId,
                    val reason: EndReason,
                    override val timestamp: Timestamp
                ) : RoomEvent

                @Serializable
                @SerialName("room_message_sent")
                data class RoomMessageSent(
                    override val roomId: RoomId,
                    override val authorId: SessionId,
                    val message: String,
                    val messageId: MessageId,
                    val context: JsonObject,
                    override val timestamp: Timestamp
                ) : RoomEvent

                @Serializable
                @SerialName("fake_room_message_sent")
                data class FakeRoomMessageSent(
                    override val roomId: RoomId,
                    override val authorId: SessionId,
                    val messageId: MessageId,
                    override val timestamp: Timestamp
                ) : RoomEvent

                @Serializable
                @SerialName("room_custom_message_sent")
                data class RoomCustomMessageSent(
                    override val roomId: RoomId,
                    override val authorId: SessionId,
                    val message: String,
                    val messageId: MessageId,
                    val subtag: String,
                    val context: JsonObject,
                    override val timestamp: Timestamp
                ) : RoomEvent

                @Serializable
                @SerialName("room_typing_sent")
                data class RoomTypingSent(
                    override val roomId: RoomId,
                    override val authorId: SessionId,
                    val preview: String?,
                    override val timestamp: Timestamp
                ) : RoomEvent

                @Serializable
                @SerialName("room_mark_sent")
                data class RoomMarkSent(
                    override val roomId: RoomId,
                    override val authorId: SessionId,
                    override val timestamp: Timestamp
                ) : RoomEvent

                @Serializable
                @SerialName("room_message_delivered")
                data class RoomMessageDelivered(
                    override val roomId: RoomId,
                    override val authorId: SessionId,
                    val messageId: NormalizedEventId,
                    override val timestamp: Timestamp
                ) : RoomEvent

                @Serializable
                @SerialName("room_message_updated")
                data class RoomMessageUpdated(
                    override val roomId: RoomId,
                    override val authorId: SessionId,
                    override val timestamp: Timestamp,
                    val messageId: MessageId,
                    val context: JsonObject
                ) : RoomEvent
            }
        }

        sealed interface ChatEvent : DomainEvent {
            companion object {
                @Serializable
                @SerialName("chat_received")
                data class ChatReceived(
                    val eventId: NormalizedEventId,
                    val message: NormalizedEvent,
                    val ref: String?
                ) : ChatEvent {
                    companion object {
                        @Serializable
                        data class NormalizedEvent(
                            val id: NormalizedEventId,
                            val authorId: SessionId,
                            val channelId: ChannelId,
                            // TODO: val tag: EventTag,
                            val data: JsonObject,
                            val timestamp: Timestamp
                        )
                    }
                }
            }
        }

        sealed interface CallEvent : DomainEvent {
            val callId: CallId
            val timestamp: Timestamp

            companion object {
                @Serializable
                @SerialName("call_answered")
                data class CallAnswered(
                    override val callId: CallId,
                    val authorId: SessionId,
                    val metadata: JsonObject?,
                    override val timestamp: Timestamp
                ) : CallEvent

                @Serializable
                @SerialName("audio_stream_toggled")
                data class AudioStreamToggled(
                    override val callId: CallId,
                    val userId: SessionId,
                    val enabled: Boolean,
                    override val timestamp: Timestamp
                ) : CallEvent

                @Serializable
                @SerialName("call_handled_on_device")
                data class CallHandledOnDevice(
                    override val callId: CallId,
                    val authorId: SessionId,
                    val device: DeviceId,
                    override val timestamp: Timestamp
                ) : CallEvent

                @Serializable
                @SerialName("call_created")
                data class CallCreated(
                    override val callId: CallId,
                    val authorId: SessionId,
                    override val timestamp: Timestamp
                ) : CallEvent

                @Serializable
                @SerialName("device_offline")
                data class DeviceOffline(
                    override val callId: CallId,
                    val userId: SessionId,
                    val deviceId: DeviceId,
                    override val timestamp: Timestamp
                ) : CallEvent

                @Serializable
                @SerialName("device_online")
                data class DeviceOnline(
                    override val callId: CallId,
                    val userId: SessionId,
                    val deviceId: DeviceId,
                    override val timestamp: Timestamp
                ) : CallEvent

                @Serializable
                @SerialName("call_ended")
                data class CallEnded(
                    override val callId: CallId,
                    val reason: EndReason,
                    override val timestamp: Timestamp
                ) : CallEvent

                @Serializable
                @SerialName("call_invited")
                data class CallInvited(
                    override val callId: CallId,
                    val authorId: SessionId,
                    val invitee: SessionId,
                    val metadata: JsonObject?,
                    override val timestamp: Timestamp
                ) : CallEvent

                @Serializable
                @SerialName("call_joined")
                data class CallJoined(
                    override val callId: CallId,
                    val authorId: SessionId,
                    override val timestamp: Timestamp
                ) : CallEvent

                @Serializable
                @SerialName("call_left")
                data class CallLeft(
                    override val callId: CallId,
                    val authorId: SessionId,
                    val reason: EndReason,
                    override val timestamp: Timestamp
                ) : CallEvent

                @Serializable
                @SerialName("call_rejected")
                data class CallRejected(
                    override val callId: CallId,
                    val authorId: SessionId,
                    val reason: EndReason,
                    override val timestamp: Timestamp
                ) : CallEvent

                @Serializable
                @SerialName("video_stream_toggled")
                data class VideoStreamToggled(
                    override val callId: CallId,
                    val userId: SessionId,
                    val enabled: Boolean,
                    override val timestamp: Timestamp,
                    val content: VideoType
                ) : CallEvent {
                    companion object {
                        @Serializable
                        enum class VideoType {
                            @SerialName("camera")
                            CAMERA,

                            @SerialName("screen")
                            SCREEN
                        }
                    }
                }
            }
        }

        sealed interface ExternalEvent : DomainEvent {
            companion object {
                @Serializable
                @SerialName("guest_profile_updated")
                data class GuestProfileUpdated(
                    val roomId: RoomId,
                    val customerId: LeadId,
                    val firstName: String?,
                    val lastName: String?,
                    val email: String?,
                    val phone: Phone?,
                    val locale: Locale,
                    @Serializable(with = ZoneIdSerializer::class)
                    val zoneId: ZoneId,
                    val backOfficeData: List<BackofficeField>,
                    val tags: List<String>,
                    val tagGroupId: TagGroupId?,
                    val topics: List<String>
                ) : ExternalEvent {
                    companion object {
                        @Serializable
                        data class Phone(
                            val region: String,
                            val number: String
                        )

                        @Serializable
                        data class BackofficeField(
                            val key: String,
                            val value: JsonElement,
                            val displayName: String?
                        )
                    }
                }

                @Serializable
                @SerialName("last_adviser_timestamp_removed")
                data class LastAdviserTimestampRemoved(
                    val roomId: RoomId
                ) : ExternalEvent

                @Serializable
                @SerialName("last_adviser_timestamp_set")
                data class LastAdviserTimestampSet(
                    val roomId: RoomId, val timestamp: Timestamp
                ) : ExternalEvent

                @Serializable
                @SerialName("conversation_snoozed")
                data class ConversationSnoozed(
                    val roomId: RoomId
                ) : ExternalEvent

                @Serializable
                @SerialName("conversation_unsnoozed")
                data class ConversationUnsnoozed(
                    val roomId: RoomId
                ) : ExternalEvent

                @Serializable
                @SerialName("notification_upcoming_meeting")
                data class NotificationUpcomingMeeting(
                    val meetingId: MeetingId,
                    val roomId: RoomId,
                    val start: Timestamp,
                    val duration: Long,
                    val minutesToMeeting: Long,
                    val guestId: LeadId,
                    val guestName: String,
                    val langTag: Locale
                ) : ExternalEvent

                @Serializable
                @SerialName("agent_status_updated")
                data class AgentStatusUpdated(
                    val agentId: AgentId,
                    val available: AvailableSubstatus?,
                    val unready: UnreadySubstatus?,
                    val unavailable: UnavailableSubstatus?,
                    val presence: Presence,
                    val registeredForPush: Boolean,
                    val timestamp: Timestamp
                ) : ExternalEvent {
                    companion object {
                        @Serializable
                        enum class AvailableSubstatus {
                            @SerialName("chat_and_call")
                            CHAT_AND_CALL,

                            @SerialName("chat_only")
                            CHAT_ONLY
                        }

                        @Serializable
                        enum class UnreadySubstatus {
                            @SerialName("preparing")
                            PREPARING,

                            @SerialName("cool_down")
                            COOL_DOWN,

                            @SerialName("on_call")
                            ON_CALL,

                            @SerialName("after_call")
                            AFTER_CALL
                        }
                    }
                }

                @Serializable
                @SerialName("assignee_status_updated")
                data class AssigneeStatusUpdated(
                    val agentId: AgentId,
                    val areCallsPossible: Boolean,
                    val timestamp: Timestamp
                ) : ExternalEvent

                @Serializable
                @SerialName("lead_presence_updated")
                data class LeadPresenceUpdated(
                    val leadId: LeadId,
                    val presence: Presence,
                    val timestamp: Timestamp
                ) : ExternalEvent

                @Serializable
                @SerialName("unread_count_updated")
                data class UnreadCountUpdated(
                    val roomId: RoomId, val unreadCount: Int
                ) : ExternalEvent

                @Serializable
                @SerialName("unread_total_updated")
                data class UnreadTotalUpdated(
                    val tab: ConversationTab,
                    val unreadCount: Int,
                    val unreadCountNoTagGroup: Int?,
                    val unreadCountByTagGroup: Map<TagGroupId, Int>?
                ) : ExternalEvent

                @Serializable
                @SerialName("unassigned_count_updated")
                data class UnassignedCountUpdated(
                    val count: Int,
                    val countNoTagGroup: Int?,
                    val countByTagGroup: Map<TagGroupId, Int>?
                ) : ExternalEvent

                @Serializable
                @SerialName("conversation_status_changed")
                data class ConversationStatusChanged(
                    val roomId: RoomId,
                    val status: ConversationStatus,
                    val adviserId: AgentId?,
                    val timestamp: Timestamp
                ) : ExternalEvent {
                    companion object {
                        @Serializable
                        enum class ConversationStatus {
                            @SerialName("new")
                            NEW,

                            @SerialName("waiting")
                            WAITING,

                            @SerialName("inProgress")
                            IN_PROGRESS,

                            @SerialName("solved")
                            SOLVED,

                            @SerialName("unsolved")
                            UNSOLVED
                        }
                    }
                }

                @Serializable
                @SerialName("follower_added")
                data class FollowerAdded(
                    val roomId: RoomId,
                    val adviserId: AgentId,
                    val requesterId: AgentId?
                ) : ExternalEvent

                @Serializable
                @SerialName("follower_removed")
                data class FollowerRemoved(
                    val roomId: RoomId,
                    val adviserId: AgentId,
                    val requesterId: UserId?
                ) : ExternalEvent

                @Serializable
                @SerialName("meeting_scheduled")
                data class MeetingScheduled(
                    val roomId: RoomId,
                    val meetingId: MeetingId,
                    val adviserId: AgentId,
                    val start: Timestamp,
                    val duration: Long
                ) : ExternalEvent

                @Serializable
                @SerialName("meeting_rescheduled")
                data class MeetingRescheduled(
                    val roomId: RoomId,
                    val meetingId: MeetingId,
                    val adviserId: AgentId,
                    val start: Timestamp,
                    val duration: Long
                ) : ExternalEvent

                @Serializable
                @SerialName("meeting_cancelled")
                data class MeetingCancelled(
                    val roomId: RoomId,
                    val meetingId: MeetingId,
                    val adviserId: AgentId,
                    val start: Timestamp,
                    val duration: Long
                ) : ExternalEvent

                @Serializable
                @SerialName("assignee_changed")
                data class AssigneeChanged(
                    val roomId: RoomId,
                    val adviserId: AgentId,
                    val requesterId: UserId?
                ) : ExternalEvent

                @Serializable
                @SerialName("assignee_removed")
                data class AssigneeRemoved(
                    val roomId: RoomId, val adviserId: AgentId
                ) : ExternalEvent

                @Serializable
                @SerialName("all_followers_removed")
                data class AllFollowersRemoved(
                    val roomId: RoomId
                ) : ExternalEvent

                @Serializable
                @SerialName("call_unsuccessful")
                data class CallUnsuccessful(
                    val roomId: RoomId
                ) : ExternalEvent

                @Serializable
                @SerialName("agent_group_created")
                data class AgentGroupCreated(
                    val id: AgentGroupId,
                    val name: String,
                    val tags: List<TagId>,
                    val tagGroups: List<TagGroupId>,
                    val advisers: List<AgentId>
                ) : ExternalEvent

                @Serializable
                @SerialName("agent_group_updated")
                data class AgentGroupUpdated(
                    val id: AgentGroupId,
                    val name: String,
                    val tags: List<TagId>,
                    val tagGroups: List<TagGroupId>,
                    val advisers: List<AgentId>
                ) : ExternalEvent

                @Serializable
                @SerialName("adviser_tag_groups_updated")
                data class AdviserTagGroupsUpdated(
                    val id: AgentId,
                    val tagGroups: List<TagGroupId>
                ) : ExternalEvent

                @Serializable
                @SerialName("agent_group_deleted")
                data class AgentGroupDeleted(
                    val id: AgentGroupId
                ) : ExternalEvent
            }
        }
    }
}
