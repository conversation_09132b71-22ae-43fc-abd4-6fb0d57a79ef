package app.closer.mobile_sdk.artichoke.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
enum class EndReason {
    @SerialName("terminated")
    TERMINATED,

    @SerialName("timeout")
    TIMEOUT,

    @SerialName("ended")
    ENDED,

    @SerialName("hangup")
    HANGUP,

    @SerialName("connection_dropped")
    CONNECTION_DROPPED,

    @SerialName("disconnected")
    DISCONNECTED,

    @SerialName("rejected")
    REJECTED,

    @SerialName("busy")
    BUSY,

    @SerialName("time_limit_exceeded")
    TIME_LIMIT_EXCEEDED
}
