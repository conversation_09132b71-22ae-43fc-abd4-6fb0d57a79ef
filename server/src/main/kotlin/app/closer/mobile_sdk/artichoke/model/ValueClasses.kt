package app.closer.mobile_sdk.artichoke.model

import kotlinx.serialization.Serializable
import kotlin.uuid.Uuid

@JvmInline
@Serializable
value class AgentId(val value: Uuid)

@JvmInline
@Serializable
value class AgentGroupId(val value: Uuid)

@JvmInline
@Serializable
value class CallId(val value: Uuid)

@JvmInline
@Serializable
value class ChannelId(val value: Uuid)

@JvmInline
@Serializable
value class LeadId(val value: Uuid)

@JvmInline
@Serializable
value class MeetingId(val value: Uuid)

@JvmInline
@Serializable
value class MessageId(val value: Uuid)

@JvmInline
@Serializable
value class NormalizedEventId(val value: Uuid)

@JvmInline
@Serializable
value class OrgId(val value: Uuid)

@JvmInline
@Serializable
value class RoomId(val value: Uuid)

@JvmInline
@Serializable
value class SessionId(val value: Uuid)

@JvmInline
@Serializable
value class TagGroupId(val value: Uuid)

@JvmInline
@Serializable
value class TagId(val value: Uuid)

@JvmInline
@Serializable
value class UserId(val value: Uuid)

@JvmInline
@Serializable
value class Locale(val value: String)

@JvmInline
@Serializable
value class SDP(val value: String)

@JvmInline
@Serializable
value class UnavailableSubstatus(val value: String)

@JvmInline
@Serializable
value class Timestamp(val value: Long)
