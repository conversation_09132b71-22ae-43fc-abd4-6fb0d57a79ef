package app.closer.mobile_sdk.artichoke.model.conversions

import app.closer.*
import app.closer.mobile_sdk.artichoke.model.*
import app.closer.mobile_sdk.artichoke.model.ConversationTab
import app.closer.mobile_sdk.artichoke.model.EndReason
import app.closer.mobile_sdk.artichoke.model.JSEP
import app.closer.mobile_sdk.artichoke.model.Presence
import app.closer.mobile_sdk.artichoke.model.SDPType
import app.closer.mobile_sdk.conversions.*
import kotlin.uuid.Uuid
import app.closer.ConversationTab as ProtoConversationTab
import app.closer.EndReason as ProtoEndReason
import app.closer.JSEP as ProtoJSEP
import app.closer.Presence as ProtoPresence
import app.closer.SDPType as ProtoSDPType

fun CallDto.toGetCallResponse(): GetCallResponse =
    GetCallResponse.newBuilder()
        .ifNotNull(id) { setId(it.value.toString()) }
        .ifNotNull(creator) { setCreator(it.value.toString()) }
        .setDirect(direct)
        .ifNotNull(orgId) { setOrgId(it.value.toString()) }
        .setRecordingEnabled(recordingEnabled)
        .addAllUsers(users.map { it.value.toString() })
        .addAllInvitees(invitees.map { it.value.toString() })
        .ifNotNull(created) { setCreated(it.toProto()) }
        .ifNotNull(ended) { setEnded(it.toProto()) }
        .build()

fun ChatStreamRequest.toDomainCommand(): DomainCommand? =
    when (eventCase) {
        ChatStreamRequest.EventCase.ROOM_SEND_MESSAGE -> {
            val msg = roomSendMessage
            DomainCommand.Companion.Room.Companion.RoomSendMessage(
                roomId = RoomId(Uuid.parse(msg.roomId)),
                body = msg.body,
                context = msg.context.toJsonObject(),
                ref = msg.ref
            )
        }

        ChatStreamRequest.EventCase.ROOM_SEND_CUSTOM_MESSAGE -> {
            val msg = roomSendCustomMessage
            DomainCommand.Companion.Room.Companion.RoomSendCustomMessage(
                roomId = RoomId(Uuid.parse(msg.roomId)),
                body = msg.body,
                subtag = msg.subtag,
                context = msg.context.toJsonObject(),
                ref = msg.ref
            )
        }

        ChatStreamRequest.EventCase.ROOM_SEND_TYPING -> {
            val msg = roomSendTyping
            DomainCommand.Companion.Room.Companion.RoomSendTyping(
                roomId = RoomId(Uuid.parse(msg.roomId)),
                body = msg.body
            )
        }

        ChatStreamRequest.EventCase.ROOM_SEND_MARK -> {
            val msg = roomSendMark
            DomainCommand.Companion.Room.Companion.RoomSendMark(
                roomId = RoomId(Uuid.parse(msg.roomId)),
                timestamp = msg.timestamp.toDomain()
            )
        }

        ChatStreamRequest.EventCase.ROOM_CONFIRM_MESSAGE_DELIVERY -> {
            val msg = roomConfirmMessageDelivery
            DomainCommand.Companion.Room.Companion.RoomConfirmMessageDelivery(
                roomId = RoomId(Uuid.parse(msg.roomId)),
                eventId = NormalizedEventId(Uuid.parse(msg.eventId)),
                timestamp = msg.timestamp.toDomain()
            )
        }

        ChatStreamRequest.EventCase.AUDIO_STREAM_TOGGLE -> {
            val msg = audioStreamToggle
            DomainCommand.Companion.Call.Companion.AudioStreamToggle(
                callId = CallId(Uuid.parse(msg.callId)),
                enabled = msg.enabled,
                timestamp = msg.timestamp.toDomain()
            )
        }

        ChatStreamRequest.EventCase.VIDEO_STREAM_TOGGLE -> {
            val msg = videoStreamToggle
            DomainCommand.Companion.Call.Companion.VideoStreamToggle(
                callId = CallId(Uuid.parse(msg.callId)),
                enabled = msg.enabled,
                timestamp = msg.timestamp.toDomain(),
                content = when (msg.content) {
                    VideoType.VIDEO_TYPE_UNSPECIFIED -> null
                    VideoType.VIDEO_TYPE_CAMERA -> DomainCommand.Companion.Call.Companion.VideoStreamToggle.Companion.VideoType.CAMERA
                    VideoType.VIDEO_TYPE_SCREEN -> DomainCommand.Companion.Call.Companion.VideoStreamToggle.Companion.VideoType.SCREEN
                    VideoType.UNRECOGNIZED -> null
                }
            )
        }

        ChatStreamRequest.EventCase.RTC_SEND_DESCRIPTION -> {
            val msg = rtcSendDescription
            DomainCommand.Companion.RTCSignalling.Companion.RtcSendDescription(
                callId = CallId(Uuid.parse(msg.callId)),
                peer = SessionId(Uuid.parse(msg.peer)),
                sdp = JSEP(
                    type = msg.sdp.type.toDomain(),
                    sdp = SDP(msg.sdp.sdp)
                )
            )
        }

        ChatStreamRequest.EventCase.RTC_SEND_CANDIDATE -> {
            val msg = rtcSendCandidate
            DomainCommand.Companion.RTCSignalling.Companion.RtcSendCandidate(
                callId = CallId(Uuid.parse(msg.callId)),
                peer = SessionId(Uuid.parse(msg.peer)),
                iceCandidate = msg.iceCandidate?.let {
                    DomainCommand.Companion.RTCSignalling.Companion.RtcSendCandidate.Companion.IceCandidate(
                        candidate = it.candidate,
                        sdpMid = it.sdpMid,
                        sdpMLineIndex = it.sdpMLineIndex
                    )
                }
            )
        }

        ChatStreamRequest.EventCase.INPUT_HEARTBEAT -> {
            val msg = inputHeartbeat
            DomainCommand.Companion.InputHeartbeat(
                timestamp = msg.timestamp.toDomain()
            )
        }

        ChatStreamRequest.EventCase.EVENT_NOT_SET, null -> null
    }

fun DomainEvent.toChatStreamResponse(): ChatStreamResponse? = when (this) {
    is DomainEvent.Companion.Hello ->
        ChatStreamResponse.newBuilder().setHello(
            Hello.newBuilder()
                .setDeviceId(deviceId.value.toString())
                .setTimestamp(timestamp.toProto())
                .setHeartbeatTimeout(heartbeatTimeout)
                .setReconnectDelay(reconnectDelay)
        ).build()

    is DomainEvent.Companion.OutputHeartbeat ->
        ChatStreamResponse.newBuilder().setOutputHeartbeat(
            OutputHeartbeat.newBuilder().setTimestamp(timestamp.toProto())
        ).build()

    is DomainEvent.Companion.Disconnect ->
        ChatStreamResponse.newBuilder().setDisconnect(
            Disconnect.newBuilder().setDeviceId(deviceId.value.toString())
        ).build()

    is DomainEvent.Companion.Unauthorized ->
        ChatStreamResponse.newBuilder().setUnauthorized(Unauthorized.getDefaultInstance()).build()

    is DomainEvent.Companion.ErrorEvent.Companion.Error ->
        ChatStreamResponse.newBuilder().setError(
            ErrorEvent.newBuilder().setReason(reason)
        ).build()

    is DomainEvent.Companion.RTCSignallingEvent.Companion.RtcDescriptionSent ->
        ChatStreamResponse.newBuilder().setRtcDescriptionSent(
            RtcDescriptionSent.newBuilder()
                .setCallId(callId.value.toString())
                .setSender(sender.value.toString())
                .setSdp(
                    ProtoJSEP.newBuilder()
                        .setType(sdp.type.toProto())
                        .setSdp(sdp.sdp.value)
                )
        ).build()

    is DomainEvent.Companion.RTCSignallingEvent.Companion.RtcCandidateSent ->
        ChatStreamResponse.newBuilder().setRtcCandidateSent(
            RtcCandidateSent.newBuilder()
                .setCallId(callId.value.toString())
                .setSender(sender.value.toString())
                .ifNotNull(candidate) {
                    it.run {
                        setCandidate(
                            IceCandidate.newBuilder()
                                .setCandidate(candidate)
                                .also { builder -> sdpMid?.let { builder.setSdpMid(it) } }
                                .also { builder -> sdpMLineIndex?.let { builder.setSdpMLineIndex(it) } }
                        )
                    }
                }
        ).build()

    is DomainEvent.Companion.RoomEvent.Companion.RoomCreated ->
        ChatStreamResponse.newBuilder().setRoomCreated(
            RoomCreated.newBuilder()
                .setRoomId(roomId.value.toString())
                .setAuthorId(authorId.value.toString())
                .setTimestamp(timestamp.toProto())
        ).build()

    is DomainEvent.Companion.RoomEvent.Companion.RoomInvited ->
        ChatStreamResponse.newBuilder().setRoomInvited(
            RoomInvited.newBuilder()
                .setRoomId(roomId.value.toString())
                .setAuthorId(authorId.value.toString())
                .setInvitee(invitee.value.toString())
                .ifNotNull(metadata) { setMetadata(it.toStruct()) }
                .setTimestamp(timestamp.toProto())
        ).build()

    is DomainEvent.Companion.RoomEvent.Companion.RoomJoined ->
        ChatStreamResponse.newBuilder().setRoomJoined(
            RoomJoined.newBuilder()
                .setRoomId(roomId.value.toString())
                .setAuthorId(authorId.value.toString())
                .setTimestamp(timestamp.toProto())
        ).build()

    is DomainEvent.Companion.RoomEvent.Companion.RoomLeft ->
        ChatStreamResponse.newBuilder().setRoomLeft(
            RoomLeft.newBuilder()
                .setRoomId(roomId.value.toString())
                .setAuthorId(authorId.value.toString())
                .setReason(reason.toProto())
                .setTimestamp(timestamp.toProto())
        ).build()

    is DomainEvent.Companion.RoomEvent.Companion.RoomMessageSent ->
        ChatStreamResponse.newBuilder().setRoomMessageSent(
            RoomMessageSent.newBuilder()
                .setRoomId(roomId.value.toString())
                .setAuthorId(authorId.value.toString())
                .setMessage(message)
                .setMessageId(messageId.value.toString())
                .setContext(context.toStruct())
                .setTimestamp(timestamp.toProto())
        ).build()

    is DomainEvent.Companion.RoomEvent.Companion.FakeRoomMessageSent ->
        ChatStreamResponse.newBuilder().setFakeRoomMessageSent(
            FakeRoomMessageSent.newBuilder()
                .setRoomId(roomId.value.toString())
                .setAuthorId(authorId.value.toString())
                .setMessageId(messageId.value.toString())
                .setTimestamp(timestamp.toProto())
        ).build()

    is DomainEvent.Companion.RoomEvent.Companion.RoomCustomMessageSent ->
        ChatStreamResponse.newBuilder().setRoomCustomMessageSent(
            RoomCustomMessageSent.newBuilder()
                .setRoomId(roomId.value.toString())
                .setAuthorId(authorId.value.toString())
                .setMessage(message)
                .setMessageId(messageId.value.toString())
                .setSubtag(subtag)
                .setContext(context.toStruct())
                .setTimestamp(timestamp.toProto())
        ).build()

    is DomainEvent.Companion.RoomEvent.Companion.RoomTypingSent ->
        ChatStreamResponse.newBuilder().setRoomTypingSent(
            RoomTypingSent.newBuilder()
                .setRoomId(roomId.value.toString())
                .setAuthorId(authorId.value.toString())
                .ifNotNull(preview) { setPreview(it) }
                .setTimestamp(timestamp.toProto())
        ).build()

    is DomainEvent.Companion.RoomEvent.Companion.RoomMarkSent ->
        ChatStreamResponse.newBuilder().setRoomMarkSent(
            RoomMarkSent.newBuilder()
                .setRoomId(roomId.value.toString())
                .setAuthorId(authorId.value.toString())
                .setTimestamp(timestamp.toProto())
        ).build()

    is DomainEvent.Companion.RoomEvent.Companion.RoomMessageDelivered ->
        ChatStreamResponse.newBuilder().setRoomMessageDelivered(
            RoomMessageDelivered.newBuilder()
                .setRoomId(roomId.value.toString())
                .setAuthorId(authorId.value.toString())
                .setMessageId(messageId.value.toString())
                .setTimestamp(timestamp.toProto())
        ).build()

    is DomainEvent.Companion.RoomEvent.Companion.RoomMessageUpdated ->
        ChatStreamResponse.newBuilder().setRoomMessageUpdated(
            RoomMessageUpdated.newBuilder()
                .setRoomId(roomId.value.toString())
                .setAuthorId(authorId.value.toString())
                .setTimestamp(timestamp.toProto())
                .setMessageId(messageId.value.toString())
                .setContext(context.toStruct())
        ).build()

    is DomainEvent.Companion.ChatEvent.Companion.ChatReceived ->
        ChatStreamResponse.newBuilder().setChatReceived(
            ChatReceived.newBuilder()
                .setEventId(eventId.value.toString())
                .setMessage(
                    NormalizedEvent.newBuilder()
                        .setId(message.id.value.toString())
                        .setAuthorId(message.authorId.value.toString())
                        .setChannelId(message.channelId.value.toString())
                        .setData(message.data.toStruct())
                        .setTimestamp(message.timestamp.toProto())
                )
                .ifNotNull(ref) { setRef(it) }
        ).build()

    is DomainEvent.Companion.CallEvent.Companion.CallAnswered ->
        ChatStreamResponse.newBuilder().setCallAnswered(
            CallAnswered.newBuilder()
                .setCallId(callId.value.toString())
                .setAuthorId(authorId.value.toString())
                .ifNotNull(metadata) { setMetadata(it.toStruct()) }
                .setTimestamp(timestamp.toProto())
        ).build()

    is DomainEvent.Companion.CallEvent.Companion.AudioStreamToggled ->
        ChatStreamResponse.newBuilder().setAudioStreamToggled(
            AudioStreamToggled.newBuilder()
                .setCallId(callId.value.toString())
                .setUserId(userId.value.toString())
                .setEnabled(enabled)
                .setTimestamp(timestamp.toProto())
        ).build()

    is DomainEvent.Companion.CallEvent.Companion.CallHandledOnDevice ->
        ChatStreamResponse.newBuilder().setCallHandledOnDevice(
            CallHandledOnDevice.newBuilder()
                .setCallId(callId.value.toString())
                .setAuthorId(authorId.value.toString())
                .setDevice(device.value.toString())
                .setTimestamp(timestamp.toProto())
        ).build()

    is DomainEvent.Companion.CallEvent.Companion.CallCreated ->
        ChatStreamResponse.newBuilder().setCallCreated(
            CallCreated.newBuilder()
                .setCallId(callId.value.toString())
                .setAuthorId(authorId.value.toString())
                .setTimestamp(timestamp.toProto())
        ).build()

    is DomainEvent.Companion.CallEvent.Companion.DeviceOffline ->
        ChatStreamResponse.newBuilder().setDeviceOffline(
            DeviceOffline.newBuilder()
                .setCallId(callId.value.toString())
                .setUserId(userId.value.toString())
                .setDeviceId(deviceId.value.toString())
                .setTimestamp(timestamp.toProto())
        ).build()

    is DomainEvent.Companion.CallEvent.Companion.DeviceOnline ->
        ChatStreamResponse.newBuilder().setDeviceOnline(
            DeviceOnline.newBuilder()
                .setCallId(callId.value.toString())
                .setUserId(userId.value.toString())
                .setDeviceId(deviceId.value.toString())
                .setTimestamp(timestamp.toProto())
        ).build()

    is DomainEvent.Companion.CallEvent.Companion.CallEnded ->
        ChatStreamResponse.newBuilder().setCallEnded(
            CallEnded.newBuilder()
                .setCallId(callId.value.toString())
                .setReason(reason.toProto())
                .setTimestamp(timestamp.toProto())
        ).build()

    is DomainEvent.Companion.CallEvent.Companion.CallInvited ->
        ChatStreamResponse.newBuilder().setCallInvited(
            CallInvited.newBuilder()
                .setCallId(callId.value.toString())
                .setAuthorId(authorId.value.toString())
                .setInvitee(invitee.value.toString())
                .ifNotNull(metadata) { setMetadata(it.toStruct()) }
                .setTimestamp(timestamp.toProto())
        ).build()

    is DomainEvent.Companion.CallEvent.Companion.CallJoined ->
        ChatStreamResponse.newBuilder().setCallJoined(
            CallJoined.newBuilder()
                .setCallId(callId.value.toString())
                .setAuthorId(authorId.value.toString())
                .setTimestamp(timestamp.toProto())
        ).build()

    is DomainEvent.Companion.CallEvent.Companion.CallLeft ->
        ChatStreamResponse.newBuilder().setCallLeft(
            CallLeft.newBuilder()
                .setCallId(callId.value.toString())
                .setAuthorId(authorId.value.toString())
                .setReason(reason.toProto())
                .setTimestamp(timestamp.toProto())
        ).build()

    is DomainEvent.Companion.CallEvent.Companion.CallRejected ->
        ChatStreamResponse.newBuilder().setCallRejected(
            CallRejected.newBuilder()
                .setCallId(callId.value.toString())
                .setAuthorId(authorId.value.toString())
                .setReason(reason.toProto())
                .setTimestamp(timestamp.toProto())
        ).build()

    is DomainEvent.Companion.CallEvent.Companion.VideoStreamToggled ->
        ChatStreamResponse.newBuilder().setVideoStreamToggled(
            VideoStreamToggled.newBuilder()
                .setCallId(callId.value.toString())
                .setUserId(userId.value.toString())
                .setEnabled(enabled)
                .setTimestamp(timestamp.toProto())
                .setContent(
                    when (content) {
                        DomainEvent.Companion.CallEvent.Companion.VideoStreamToggled.Companion.VideoType.CAMERA -> VideoType.VIDEO_TYPE_CAMERA
                        DomainEvent.Companion.CallEvent.Companion.VideoStreamToggled.Companion.VideoType.SCREEN -> VideoType.VIDEO_TYPE_SCREEN
                    }
                )
        ).build()

    is DomainEvent.Companion.ExternalEvent.Companion.GuestProfileUpdated ->
        ChatStreamResponse.newBuilder().setGuestProfileUpdated(
            GuestProfileUpdated.newBuilder()
                .setRoomId(roomId.value.toString())
                .setCustomerId(customerId.value.toString())
                .ifNotNull(firstName) { setFirstName(it) }
                .ifNotNull(lastName) { setLastName(it) }
                .ifNotNull(email) { setEmail(it) }
                .ifNotNull(phone) { setPhone(Phone.newBuilder().setRegion(it.region).setNumber(it.number)) }
                .setLocale(locale.value)
                .setZoneId(zoneId.id)
                .addAllBackOfficeData(backOfficeData.map {
                    BackofficeField.newBuilder()
                        .setKey(it.key)
                        .setValue(it.value.toValue())
                        .also { builder -> it.displayName?.let { builder.setDisplayName(it) } }
                        .build()
                })
                .addAllTags(tags)
                .ifNotNull(tagGroupId) { setTagGroupId(it.value.toString()) }
                .addAllTopics(topics)
        ).build()

    is DomainEvent.Companion.ExternalEvent.Companion.LastAdviserTimestampRemoved ->
        ChatStreamResponse.newBuilder().setLastAdviserTimestampRemoved(
            LastAdviserTimestampRemoved.newBuilder()
                .setRoomId(roomId.value.toString())
        ).build()

    is DomainEvent.Companion.ExternalEvent.Companion.LastAdviserTimestampSet ->
        ChatStreamResponse.newBuilder().setLastAdviserTimestampSet(
            LastAdviserTimestampSet.newBuilder()
                .setRoomId(roomId.value.toString())
                .setTimestamp(timestamp.toProto())
        ).build()

    is DomainEvent.Companion.ExternalEvent.Companion.ConversationSnoozed ->
        ChatStreamResponse.newBuilder().setConversationSnoozed(
            ConversationSnoozed.newBuilder()
                .setRoomId(roomId.value.toString())
        ).build()

    is DomainEvent.Companion.ExternalEvent.Companion.ConversationUnsnoozed ->
        ChatStreamResponse.newBuilder().setConversationUnsnoozed(
            ConversationUnsnoozed.newBuilder()
                .setRoomId(roomId.value.toString())
        ).build()

    is DomainEvent.Companion.ExternalEvent.Companion.NotificationUpcomingMeeting ->
        ChatStreamResponse.newBuilder().setNotificationUpcomingMeeting(
            NotificationUpcomingMeeting.newBuilder()
                .setMeetingId(meetingId.value.toString())
                .setRoomId(roomId.value.toString())
                .setStart(start.toProto())
                .setDuration(duration)
                .setMinutesToMeeting(minutesToMeeting)
                .setGuestId(guestId.value.toString())
                .setGuestName(guestName)
                .setLangTag(langTag.value)
        ).build()

    is DomainEvent.Companion.ExternalEvent.Companion.AgentStatusUpdated ->
        ChatStreamResponse.newBuilder().setAgentStatusUpdated(
            AgentStatusUpdated.newBuilder()
                .setAgentId(agentId.value.toString())
                .setAvailable(
                    when (available) {
                        DomainEvent.Companion.ExternalEvent.Companion.AgentStatusUpdated.Companion.AvailableSubstatus.CHAT_AND_CALL -> AvailableSubstatus.AVAILABLE_SUBSTATUS_CHAT_AND_CALL
                        DomainEvent.Companion.ExternalEvent.Companion.AgentStatusUpdated.Companion.AvailableSubstatus.CHAT_ONLY -> AvailableSubstatus.AVAILABLE_SUBSTATUS_CHAT_ONLY
                        null -> AvailableSubstatus.AVAILABLE_SUBSTATUS_UNSPECIFIED
                    }
                )
                .setUnready(
                    when (unready) {
                        DomainEvent.Companion.ExternalEvent.Companion.AgentStatusUpdated.Companion.UnreadySubstatus.PREPARING -> UnreadySubstatus.UNREADY_SUBSTATUS_PREPARING
                        DomainEvent.Companion.ExternalEvent.Companion.AgentStatusUpdated.Companion.UnreadySubstatus.COOL_DOWN -> UnreadySubstatus.UNREADY_SUBSTATUS_COOL_DOWN
                        DomainEvent.Companion.ExternalEvent.Companion.AgentStatusUpdated.Companion.UnreadySubstatus.ON_CALL -> UnreadySubstatus.UNREADY_SUBSTATUS_ON_CALL
                        DomainEvent.Companion.ExternalEvent.Companion.AgentStatusUpdated.Companion.UnreadySubstatus.AFTER_CALL -> UnreadySubstatus.UNREADY_SUBSTATUS_AFTER_CALL
                        null -> UnreadySubstatus.UNREADY_SUBSTATUS_UNSPECIFIED
                    }
                )
                .ifNotNull(unavailable) { setUnavailable(it.value) }
                .setPresence(presence.toProto())
                .setRegisteredForPush(registeredForPush)
                .setTimestamp(timestamp.toProto())
        ).build()

    is DomainEvent.Companion.ExternalEvent.Companion.AssigneeStatusUpdated ->
        ChatStreamResponse.newBuilder().setAssigneeStatusUpdated(
            AssigneeStatusUpdated.newBuilder()
                .setAgentId(agentId.value.toString())
                .setAreCallsPossible(areCallsPossible)
                .setTimestamp(timestamp.toProto())
        ).build()

    is DomainEvent.Companion.ExternalEvent.Companion.LeadPresenceUpdated ->
        ChatStreamResponse.newBuilder().setLeadPresenceUpdated(
            LeadPresenceUpdated.newBuilder()
                .setLeadId(leadId.value.toString())
                .setPresence(presence.toProto())
                .setTimestamp(timestamp.toProto())
        ).build()

    is DomainEvent.Companion.ExternalEvent.Companion.UnreadCountUpdated ->
        ChatStreamResponse.newBuilder().setUnreadCountUpdated(
            UnreadCountUpdated.newBuilder()
                .setRoomId(roomId.value.toString())
                .setUnreadCount(unreadCount)
        ).build()

    is DomainEvent.Companion.ExternalEvent.Companion.UnreadTotalUpdated ->
        ChatStreamResponse.newBuilder().setUnreadTotalUpdated(
            UnreadTotalUpdated.newBuilder()
                .setTab(
                    when (tab) {
                        ConversationTab.WAITING -> ProtoConversationTab.CONVERSATION_TAB_WAITING
                        ConversationTab.YOURS -> ProtoConversationTab.CONVERSATION_TAB_YOURS
                        ConversationTab.FOLLOWED -> ProtoConversationTab.CONVERSATION_TAB_FOLLOWED
                        ConversationTab.IN_PROGRESS -> ProtoConversationTab.CONVERSATION_TAB_IN_PROGRESS
                        ConversationTab.CLOSED -> ProtoConversationTab.CONVERSATION_TAB_CLOSED
                    }
                )
                .setUnreadCount(unreadCount)
                .ifNotNull(unreadCountNoTagGroup) { setUnreadCountNoTagGroup(it) }
                .ifNotNull(unreadCountByTagGroup) { putAllUnreadCountByTagGroup(it.mapKeys { it.key.value.toString() }) }
        ).build()

    is DomainEvent.Companion.ExternalEvent.Companion.UnassignedCountUpdated ->
        ChatStreamResponse.newBuilder().setUnassignedCountUpdated(
            UnassignedCountUpdated.newBuilder()
                .setCount(count)
                .ifNotNull(countNoTagGroup) { setCountNoTagGroup(it) }
                .ifNotNull(countByTagGroup) { putAllCountByTagGroup(it.mapKeys { it.key.value.toString() }) }
        ).build()

    is DomainEvent.Companion.ExternalEvent.Companion.ConversationStatusChanged ->
        ChatStreamResponse.newBuilder().setConversationStatusChanged(
            ConversationStatusChanged.newBuilder()
                .setRoomId(roomId.value.toString())
                .setStatus(
                    when (status) {
                        DomainEvent.Companion.ExternalEvent.Companion.ConversationStatusChanged.Companion.ConversationStatus.NEW -> ConversationStatus.CONVERSATION_STATUS_NEW
                        DomainEvent.Companion.ExternalEvent.Companion.ConversationStatusChanged.Companion.ConversationStatus.WAITING -> ConversationStatus.CONVERSATION_STATUS_WAITING
                        DomainEvent.Companion.ExternalEvent.Companion.ConversationStatusChanged.Companion.ConversationStatus.IN_PROGRESS -> ConversationStatus.CONVERSATION_STATUS_IN_PROGRESS
                        DomainEvent.Companion.ExternalEvent.Companion.ConversationStatusChanged.Companion.ConversationStatus.SOLVED -> ConversationStatus.CONVERSATION_STATUS_SOLVED
                        DomainEvent.Companion.ExternalEvent.Companion.ConversationStatusChanged.Companion.ConversationStatus.UNSOLVED -> ConversationStatus.CONVERSATION_STATUS_UNSOLVED
                    }
                )
                .ifNotNull(adviserId) { setAdviserId(it.value.toString()) }
                .setTimestamp(timestamp.toProto())
        ).build()

    is DomainEvent.Companion.ExternalEvent.Companion.FollowerAdded ->
        ChatStreamResponse.newBuilder().setFollowerAdded(
            FollowerAdded.newBuilder()
                .setRoomId(roomId.value.toString())
                .setAdviserId(adviserId.value.toString())
                .ifNotNull(requesterId) { setRequesterId(it.value.toString()) }
        ).build()

    is DomainEvent.Companion.ExternalEvent.Companion.FollowerRemoved ->
        ChatStreamResponse.newBuilder().setFollowerRemoved(
            FollowerRemoved.newBuilder()
                .setRoomId(roomId.value.toString())
                .setAdviserId(adviserId.value.toString())
                .ifNotNull(requesterId) { setRequesterId(it.value.toString()) }
        ).build()

    is DomainEvent.Companion.ExternalEvent.Companion.MeetingScheduled ->
        ChatStreamResponse.newBuilder().setMeetingScheduled(
            MeetingScheduled.newBuilder()
                .setRoomId(roomId.value.toString())
                .setMeetingId(meetingId.value.toString())
                .setAdviserId(adviserId.value.toString())
                .setStart(start.toProto())
                .setDuration(duration)
        ).build()

    is DomainEvent.Companion.ExternalEvent.Companion.MeetingRescheduled ->
        ChatStreamResponse.newBuilder().setMeetingRescheduled(
            MeetingRescheduled.newBuilder()
                .setRoomId(roomId.value.toString())
                .setMeetingId(meetingId.value.toString())
                .setAdviserId(adviserId.value.toString())
                .setStart(start.toProto())
                .setDuration(duration)
        ).build()

    is DomainEvent.Companion.ExternalEvent.Companion.MeetingCancelled ->
        ChatStreamResponse.newBuilder().setMeetingCancelled(
            MeetingCancelled.newBuilder()
                .setRoomId(roomId.value.toString())
                .setMeetingId(meetingId.value.toString())
                .setAdviserId(adviserId.value.toString())
                .setStart(start.toProto())
                .setDuration(duration)
        ).build()

    is DomainEvent.Companion.ExternalEvent.Companion.AssigneeChanged ->
        ChatStreamResponse.newBuilder().setAssigneeChanged(
            AssigneeChanged.newBuilder()
                .setRoomId(roomId.value.toString())
                .setAdviserId(adviserId.value.toString())
                .ifNotNull(requesterId) { setRequesterId(it.value.toString()) }
        ).build()

    is DomainEvent.Companion.ExternalEvent.Companion.AssigneeRemoved ->
        ChatStreamResponse.newBuilder().setAssigneeRemoved(
            AssigneeRemoved.newBuilder()
                .setRoomId(roomId.value.toString())
                .setAdviserId(adviserId.value.toString())
        ).build()

    is DomainEvent.Companion.ExternalEvent.Companion.AllFollowersRemoved ->
        ChatStreamResponse.newBuilder().setAllFollowersRemoved(
            AllFollowersRemoved.newBuilder()
                .setRoomId(roomId.value.toString())
        ).build()

    is DomainEvent.Companion.ExternalEvent.Companion.CallUnsuccessful ->
        ChatStreamResponse.newBuilder().setCallUnsuccessful(
            CallUnsuccessful.newBuilder()
                .setRoomId(roomId.value.toString())
        ).build()

    is DomainEvent.Companion.ExternalEvent.Companion.AgentGroupCreated ->
        ChatStreamResponse.newBuilder().setAgentGroupCreated(
            AgentGroupCreated.newBuilder()
                .setId(id.value.toString())
                .setName(name)
                .addAllTags(tags.map { it.value.toString() })
                .addAllTagGroups(tagGroups.map { it.value.toString() })
                .addAllAdvisers(advisers.map { it.value.toString() })
        ).build()

    is DomainEvent.Companion.ExternalEvent.Companion.AgentGroupUpdated ->
        ChatStreamResponse.newBuilder().setAgentGroupUpdated(
            AgentGroupUpdated.newBuilder()
                .setId(id.value.toString())
                .setName(name)
                .addAllTags(tags.map { it.value.toString() })
                .addAllTagGroups(tagGroups.map { it.value.toString() })
                .addAllAdvisers(advisers.map { it.value.toString() })
        ).build()

    is DomainEvent.Companion.ExternalEvent.Companion.AdviserTagGroupsUpdated ->
        ChatStreamResponse.newBuilder().setAdviserTagGroupsUpdated(
            AdviserTagGroupsUpdated.newBuilder()
                .setId(id.value.toString())
                .addAllTagGroups(tagGroups.map { it.value.toString() })
        ).build()

    is DomainEvent.Companion.ExternalEvent.Companion.AgentGroupDeleted ->
        ChatStreamResponse.newBuilder().setAgentGroupDeleted(
            AgentGroupDeleted.newBuilder()
                .setId(id.value.toString())
        ).build()
}

fun SDPType.toProto(): ProtoSDPType =
    when (this) {
        SDPType.OFFER -> ProtoSDPType.SDP_TYPE_OFFER
        SDPType.ANSWER -> ProtoSDPType.SDP_TYPE_ANSWER
    }

fun ProtoSDPType.toDomain(): SDPType =
    when (this) {
        ProtoSDPType.SDP_TYPE_OFFER -> SDPType.OFFER
        ProtoSDPType.SDP_TYPE_ANSWER -> SDPType.ANSWER
        ProtoSDPType.SDP_TYPE_UNSPECIFIED, ProtoSDPType.UNRECOGNIZED -> SDPType.OFFER
    }

fun Presence.toProto(): ProtoPresence =
    when (this) {
        Presence.AVAILABLE -> ProtoPresence.PRESENCE_AVAILABLE
        Presence.UNAVAILABLE -> ProtoPresence.PRESENCE_UNAVAILABLE
    }

fun EndReason.toProto(): ProtoEndReason =
    when (this) {
        EndReason.TERMINATED -> ProtoEndReason.END_REASON_TERMINATED
        EndReason.TIMEOUT -> ProtoEndReason.END_REASON_TIMEOUT
        EndReason.ENDED -> ProtoEndReason.END_REASON_ENDED
        EndReason.HANGUP -> ProtoEndReason.END_REASON_HANGUP
        EndReason.CONNECTION_DROPPED -> ProtoEndReason.END_REASON_CONNECTION_DROPPED
        EndReason.DISCONNECTED -> ProtoEndReason.END_REASON_DISCONNECTED
        EndReason.REJECTED -> ProtoEndReason.END_REASON_REJECTED
        EndReason.BUSY -> ProtoEndReason.END_REASON_BUSY
        EndReason.TIME_LIMIT_EXCEEDED -> ProtoEndReason.END_REASON_TIME_LIMIT_EXCEEDED
    }

fun ProtoEndReason.toDomain(): EndReason? =
    when (this) {
        ProtoEndReason.END_REASON_UNSPECIFIED -> null
        ProtoEndReason.END_REASON_TERMINATED -> EndReason.TERMINATED
        ProtoEndReason.END_REASON_TIMEOUT -> EndReason.TIMEOUT
        ProtoEndReason.END_REASON_ENDED -> EndReason.ENDED
        ProtoEndReason.END_REASON_HANGUP -> EndReason.HANGUP
        ProtoEndReason.END_REASON_CONNECTION_DROPPED -> EndReason.CONNECTION_DROPPED
        ProtoEndReason.END_REASON_DISCONNECTED -> EndReason.DISCONNECTED
        ProtoEndReason.END_REASON_REJECTED -> EndReason.REJECTED
        ProtoEndReason.END_REASON_BUSY -> EndReason.BUSY
        ProtoEndReason.END_REASON_TIME_LIMIT_EXCEEDED -> EndReason.TIME_LIMIT_EXCEEDED
        ProtoEndReason.UNRECOGNIZED -> null
    }
