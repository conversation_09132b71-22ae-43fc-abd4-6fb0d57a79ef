package app.closer.mobile_sdk.auth

import io.grpc.Metadata
import io.grpc.ServerCall
import io.grpc.kotlin.CoroutineContextServerInterceptor
import kotlinx.coroutines.slf4j.MDCContext
import kotlin.coroutines.CoroutineContext
import kotlin.uuid.Uuid

class ContextInterceptor : CoroutineContextServerInterceptor() {
    override fun coroutineContext(call: ServerCall<*, *>, headers: Metadata): CoroutineContext {
        val contextMap = mapOf(
            "method" to call.methodDescriptor.fullMethodName
        )
        val maybeApiKey = headers.get(API_KEY_METADATA_KEY)?.takeIf { !it.isBlank() }?.let { ApiKey(it) }
        val maybeDeviceId = headers.get(DEVICE_ID_METADATA_KEY)?.takeIf { !it.isBlank() }?.let { DeviceId(Uuid.parse(it)) }

        return MDCContext(contextMap)
            .run { maybeApiKey?.let { this + it } ?: this }
            .run { maybeDeviceId?.let { this + it } ?: this }
    }

    companion object {
        val API_KEY_METADATA_KEY: Metadata.Key<String> =
            Metadata.Key.of("x-api-key", Metadata.ASCII_STRING_MARSHALLER)

        val DEVICE_ID_METADATA_KEY: Metadata.Key<String> =
            Metadata.Key.of("x-device-id", Metadata.ASCII_STRING_MARSHALLER)
    }
}
