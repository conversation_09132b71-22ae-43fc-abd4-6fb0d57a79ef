package app.closer.mobile_sdk.conversions

import app.closer.mobile_sdk.artichoke.model.Timestamp
import com.google.protobuf.GeneratedMessage
import com.google.protobuf.ListValue
import com.google.protobuf.Struct
import com.google.protobuf.Value
import kotlinx.serialization.json.*
import kotlin.time.Instant
import com.google.protobuf.Timestamp as ProtoTimestamp

fun Timestamp.toProto(): ProtoTimestamp {
    val instant = Instant.fromEpochMilliseconds(value)
    return ProtoTimestamp.newBuilder().setSeconds(instant.epochSeconds).setNanos(instant.nanosecondsOfSecond).build()
}

fun ProtoTimestamp.toDomain(): Timestamp =
    Timestamp(Instant.fromEpochSeconds(seconds, nanos).toEpochMilliseconds())

fun <G : GeneratedMessage.Builder<G>, C> G.ifNotNull(value: C?, setter: G.(C) -> G): G =
    value?.let { setter(it) } ?: this

fun Struct.toJsonObject(): JsonObject =
    buildJsonObject {
        for ((key, value) in fieldsMap) put(key, value.toJsonElement())
    }

private fun ListValue.toJsonArray(): JsonArray =
    buildJsonArray {
        for (item in valuesList) add(item.toJsonElement())
    }

private fun Value.toJsonElement(): JsonElement =
    when (kindCase) {
        Value.KindCase.KIND_NOT_SET -> JsonNull
        Value.KindCase.NULL_VALUE -> JsonNull
        Value.KindCase.BOOL_VALUE -> JsonPrimitive(boolValue)
        Value.KindCase.NUMBER_VALUE -> JsonPrimitive(numberValue)
        Value.KindCase.STRING_VALUE -> JsonPrimitive(stringValue)
        Value.KindCase.STRUCT_VALUE -> structValue.toJsonObject()
        Value.KindCase.LIST_VALUE -> listValue.toJsonArray()
    }

fun JsonObject.toStruct(): Struct =
    Struct.newBuilder().also { builder ->
        for ((key, value) in this) {
            builder.putFields(key, value.toValue())
        }
    }.build()

fun JsonArray.toListValue(): ListValue =
    ListValue.newBuilder().addAllValues(map { it.toValue() }).build()

fun JsonElement.toValue(): Value =
    when (this) {
        is JsonNull -> Value.newBuilder().setNullValueValue(0).build()
        is JsonPrimitive -> when {
            isString -> Value.newBuilder().setStringValue(content).build()
            booleanOrNull != null -> Value.newBuilder().setBoolValue(boolean).build()
            longOrNull != null -> Value.newBuilder().setNumberValue(long.toDouble()).build()
            doubleOrNull != null -> Value.newBuilder().setNumberValue(double).build()
            else -> Value.newBuilder().setStringValue(content).build()
        }

        is JsonObject -> Value.newBuilder().setStructValue(toStruct()).build()
        is JsonArray -> Value.newBuilder().setListValue(toListValue()).build()
    }
