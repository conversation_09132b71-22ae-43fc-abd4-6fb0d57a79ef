package app.closer.mobile_sdk.spinner

import app.closer.mobile_sdk.spinner.model.AdviserDto
import app.closer.mobile_sdk.spinner.model.GetConversationsRequest
import app.closer.mobile_sdk.spinner.model.InboxState
import app.closer.mobile_sdk.spinner.model.LoginForm
import app.closer.mobile_sdk.toGrpcStatus
import io.github.oshai.kotlinlogging.KotlinLogging
import io.grpc.StatusException
import io.ktor.client.*
import io.ktor.client.call.*
import io.ktor.client.engine.cio.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.client.plugins.logging.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.serialization.kotlinx.json.*
import kotlinx.serialization.json.Json

private val logger = KotlinLogging.logger {}

class SpinnerClient(private val serverUrl: String) {

    private val client = HttpClient(CIO) {
        install(Logging) {
            level = LogLevel.INFO
        }
        install(ContentNegotiation) {
            json(DefaultJson)
        }
    }

    suspend fun login(email: String, password: String): AdviserDto {
        logger.info { "Calling login with email: $email" }

        val response = client.post("https://$serverUrl/api/session") {
            contentType(ContentType.Application.Json)
            setBody(LoginForm(email, password))
        }

        if (!response.status.isSuccess()) {
            val errorBody = response.bodyAsText()
            logger.error { "login failed with status: ${response.status}, body: $errorBody" }
            throw StatusException(response.status.toGrpcStatus())
        }

        val adviserDto = response.body<AdviserDto>()
        logger.info { "login returned: $adviserDto" }
        return adviserDto
    }

    suspend fun getConversations(request: GetConversationsRequest, apiKey: String): InboxState {
        logger.info { "Calling getConversations with request: $request" }

        val response = client.get("https://$serverUrl/api/conversations") {
            header("X-Api-Key", apiKey)
            parameter("tab", request.tab)
            parameter("limit", request.limit)
            parameter("offset", request.offset)
            request.tag?.forEach { tag ->
                parameter("tag", tag)
            }
            parameter("onlySnoozed", request.onlySnoozed)
            parameter("assigneeId", request.assigneeId)
            parameter("anyThreadClosedAfter", request.anyThreadClosedAfter)
            parameter("anyThreadClosedBefore", request.anyThreadClosedBefore)
            parameter("sorting", request.sorting)
            request.tagGroupId?.forEach { tagGroupId ->
                parameter("tagGroupId", tagGroupId)
            }
            parameter("closed", request.closed)
        }

        if (!response.status.isSuccess()) {
            val errorBody = response.bodyAsText()
            logger.error { "getConversations failed with status: ${response.status}, body: $errorBody" }
            throw StatusException(response.status.toGrpcStatus())
        }

        val inboxState = response.body<InboxState>()
        logger.info { "getConversations returned: $inboxState" }
        return inboxState
    }

    companion object {
        val DefaultJson = Json {
            explicitNulls = false
            ignoreUnknownKeys = true
        }
    }
}
