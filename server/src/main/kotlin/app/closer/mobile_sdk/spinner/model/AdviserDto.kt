package app.closer.mobile_sdk.spinner.model

import app.closer.mobile_sdk.artichoke.model.AgentId
import app.closer.mobile_sdk.artichoke.model.OrgId
import app.closer.mobile_sdk.auth.ApiKey
import kotlinx.serialization.Serializable

@Serializable
data class AdviserDto(
    val id: AgentId,
    val apiKey: ApiKey,
    val orgId: OrgId,
    val firstName: String,
    val lastName: String,
    val email: String
)
