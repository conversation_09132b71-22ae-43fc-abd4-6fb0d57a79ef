package app.closer.mobile_sdk.spinner.model

import app.closer.mobile_sdk.artichoke.model.AgentId
import app.closer.mobile_sdk.artichoke.model.ConversationTab
import app.closer.mobile_sdk.artichoke.model.TagGroupId
import app.closer.mobile_sdk.artichoke.model.Timestamp

data class GetConversationsRequest(
    val tab: ConversationTab? = null,
    val limit: Int,
    val offset: Timestamp? = null,
    val tag: List<String>? = null,
    val onlySnoozed: Boolean? = null,
    val assigneeId: AgentId? = null,
    val anyThreadClosedAfter: Timestamp? = null,
    val anyThreadClosedBefore: Timestamp? = null,
    val sorting: InboxSorting? = null,
    val tagGroupId: List<TagGroupId>? = null,
    val closed: Boolean? = null
)
