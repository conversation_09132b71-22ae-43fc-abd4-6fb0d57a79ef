package app.closer.mobile_sdk.spinner.model

import app.closer.mobile_sdk.artichoke.model.AgentId
import app.closer.mobile_sdk.artichoke.model.LeadId
import app.closer.mobile_sdk.artichoke.model.Presence
import app.closer.mobile_sdk.artichoke.model.RoomId
import app.closer.mobile_sdk.artichoke.model.TagGroupId
import app.closer.mobile_sdk.artichoke.model.Timestamp
import app.closer.mobile_sdk.artichoke.model.UserId
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonObject

@Serializable
data class InboxWithLeadProfileEntry(
    val room: RoomId,
    val owner: LeadId,
    val sender: UserId,
    val profile: InboxEntryProfile,
    val status: ConversationStatus,
    val assignee: AgentId? = null,
    val type: InboxEntryType,
    val message: String? = null,
    val timestamp: Timestamp,
    val lastGuestTimestamp: Timestamp,
    val lastAdviserTimestamp: Timestamp? = null,
    val threadCreatedAt: Timestamp? = null,
    val threadClosedAt: Timestamp? = null,
    val mark: Timestamp,
    val unreadCount: Int,
    val ownerPresence: Presence,
    val tags: List<String>,
    val isSnoozed: Boolean,
    val payload: JsonObject? = null,
    val snoozedStatus: SnoozedStatus? = null,
    val tagGroupId: TagGroupId? = null
)
