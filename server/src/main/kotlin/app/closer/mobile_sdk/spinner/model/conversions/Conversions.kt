package app.closer.mobile_sdk.spinner.model.conversions

import app.closer.GetConversationsResponse
import app.closer.LoginResponse
import app.closer.mobile_sdk.artichoke.model.AgentId
import app.closer.mobile_sdk.artichoke.model.ConversationTab
import app.closer.mobile_sdk.artichoke.model.TagGroupId
import app.closer.mobile_sdk.artichoke.model.Timestamp
import app.closer.mobile_sdk.artichoke.model.conversions.toProto
import app.closer.mobile_sdk.conversions.ifNotNull
import app.closer.mobile_sdk.conversions.toStruct
import app.closer.mobile_sdk.spinner.model.*
import kotlin.uuid.Uuid
import app.closer.ConversationStatus as ProtoConversationStatus
import app.closer.ConversationTab as ProtoConversationTab
import app.closer.GetConversationsRequest as ProtoGetConversationsRequest
import app.closer.InboxEntryProfile as ProtoInboxEntryProfile
import app.closer.InboxEntryType as ProtoInboxEntryType
import app.closer.InboxSorting as ProtoInboxSorting
import app.closer.InboxWithLeadProfileEntry as ProtoInboxWithLeadProfileEntry
import app.closer.Phone as ProtoPhone
import app.closer.SnoozedStatus as ProtoSnoozedStatus

fun AdviserDto.toLoginResponse(): LoginResponse =
    LoginResponse.newBuilder()
        .setId(id.value.toString())
        .setApiKey(apiKey.value)
        .setOrgId(orgId.value.toString())
        .setFirstName(firstName)
        .setLastName(lastName)
        .setEmail(email)
        .build()

fun ProtoGetConversationsRequest.toDomain(): GetConversationsRequest {
    val tab = when (this.tab) {
        ProtoConversationTab.CONVERSATION_TAB_WAITING -> ConversationTab.WAITING
        ProtoConversationTab.CONVERSATION_TAB_YOURS -> ConversationTab.YOURS
        ProtoConversationTab.CONVERSATION_TAB_FOLLOWED -> ConversationTab.FOLLOWED
        ProtoConversationTab.CONVERSATION_TAB_IN_PROGRESS -> ConversationTab.IN_PROGRESS
        ProtoConversationTab.CONVERSATION_TAB_CLOSED -> ConversationTab.CLOSED
        ProtoConversationTab.CONVERSATION_TAB_UNSPECIFIED, ProtoConversationTab.UNRECOGNIZED -> null
    }
    val sorting = when (this.sorting) {
        ProtoInboxSorting.INBOX_SORTING_NEWEST -> InboxSorting.NEWEST
        ProtoInboxSorting.INBOX_SORTING_OLDEST -> InboxSorting.OLDEST
        ProtoInboxSorting.INBOX_SORTING_UNSPECIFIED, ProtoInboxSorting.UNRECOGNIZED -> null
    }

    return GetConversationsRequest(
        tab = tab,
        limit = this.limit,
        offset = this.offset.takeIf { this.offset != 0L }?.let { Timestamp(it) },
        tag = this.tagList.takeIf { it.isNotEmpty() },
        onlySnoozed = this.onlySnoozed.takeIf { this.onlySnoozed },
        assigneeId = this.assigneeId.takeIf { this.assigneeId.isNotEmpty() }?.let { AgentId(Uuid.parse(it)) },
        anyThreadClosedAfter = this.anyThreadClosedAfter.takeIf { this.anyThreadClosedAfter != 0L }
            ?.let { Timestamp(it) },
        anyThreadClosedBefore = this.anyThreadClosedBefore.takeIf { this.anyThreadClosedBefore != 0L }
            ?.let { Timestamp(it) },
        sorting = sorting,
        tagGroupId = this.tagGroupIdList.takeIf { it.isNotEmpty() }?.map { TagGroupId(Uuid.parse(it)) },
        closed = this.closed.takeIf { this.closed }
    )
}

fun InboxState.toGetConversationsResponse(): GetConversationsResponse =
    GetConversationsResponse.newBuilder()
        .addAllEntries(this.entries.map { it.toProto() })
        .setTimestamp(this.timestamp.value)
        .build()

fun InboxWithLeadProfileEntry.toProto(): ProtoInboxWithLeadProfileEntry =
    ProtoInboxWithLeadProfileEntry.newBuilder()
        .setRoom(room.value.toString())
        .setOwner(owner.value.toString())
        .setSender(sender.value.toString())
        .setProfile(profile.toProto())
        .setStatus(status.toProto())
        .ifNotNull(assignee) { setAssignee(it.value.toString()) }
        .setType(type.toProto())
        .ifNotNull(message) { setMessage(it) }
        .setTimestamp(timestamp.value)
        .setLastGuestTimestamp(lastGuestTimestamp.value)
        .ifNotNull(lastAdviserTimestamp) { setLastAdviserTimestamp(it.value) }
        .ifNotNull(threadCreatedAt) { setThreadCreatedAt(it.value) }
        .ifNotNull(threadClosedAt) { setThreadClosedAt(it.value) }
        .setMark(mark.value)
        .setUnreadCount(unreadCount)
        .setOwnerPresence(ownerPresence.toProto())
        .addAllTags(tags)
        .setIsSnoozed(isSnoozed)
        .ifNotNull(payload) { setPayload(it.toStruct()) }
        .ifNotNull(snoozedStatus) { setSnoozedStatus(it.toProto()) }
        .ifNotNull(tagGroupId) { setTagGroupId(it.value.toString()) }
        .build()

fun InboxEntryProfile.toProto(): ProtoInboxEntryProfile =
    ProtoInboxEntryProfile.newBuilder()
        .ifNotNull(firstName) { setFirstName(it) }
        .ifNotNull(lastName) { setLastName(it) }
        .ifNotNull(email) { setEmail(it) }
        .ifNotNull(phone) { setPhone(it.toProto()) }
        .ifNotNull(randomName) { setRandomName(it) }
        .ifNotNull(searchableField) { setSearchableField(it) }
        .build()

fun Phone.toProto(): ProtoPhone =
    ProtoPhone.newBuilder()
        .setRegion(region)
        .setNumber(number)
        .build()

fun ConversationStatus.toProto(): ProtoConversationStatus =
    when (this) {
        ConversationStatus.WAITING -> ProtoConversationStatus.CONVERSATION_STATUS_WAITING
        ConversationStatus.IN_PROGRESS -> ProtoConversationStatus.CONVERSATION_STATUS_IN_PROGRESS
        ConversationStatus.SOLVED -> ProtoConversationStatus.CONVERSATION_STATUS_SOLVED
        ConversationStatus.UNSOLVED -> ProtoConversationStatus.CONVERSATION_STATUS_UNSOLVED
    }

fun InboxEntryType.toProto(): ProtoInboxEntryType =
    when (this) {
        InboxEntryType.MESSAGE -> ProtoInboxEntryType.INBOX_ENTRY_TYPE_MESSAGE
        InboxEntryType.NOTE -> ProtoInboxEntryType.INBOX_ENTRY_TYPE_NOTE
        InboxEntryType.FILES -> ProtoInboxEntryType.INBOX_ENTRY_TYPE_FILES
        InboxEntryType.MEETING_SCHEDULED -> ProtoInboxEntryType.INBOX_ENTRY_TYPE_MEETING_SCHEDULED
        InboxEntryType.MEETING_RESCHEDULED -> ProtoInboxEntryType.INBOX_ENTRY_TYPE_MEETING_RESCHEDULED
        InboxEntryType.MEETING_CANCELLED -> ProtoInboxEntryType.INBOX_ENTRY_TYPE_MEETING_CANCELLED
        InboxEntryType.CALL_STARTED -> ProtoInboxEntryType.INBOX_ENTRY_TYPE_CALL_STARTED
        InboxEntryType.CALL_ENDED -> ProtoInboxEntryType.INBOX_ENTRY_TYPE_CALL_ENDED
        InboxEntryType.FORM -> ProtoInboxEntryType.INBOX_ENTRY_TYPE_FORM
    }

fun SnoozedStatus.toProto(): ProtoSnoozedStatus =
    when (this) {
        SnoozedStatus.SNOOZED -> ProtoSnoozedStatus.SNOOZED_STATUS_SNOOZED
        SnoozedStatus.UNSNOOZED -> ProtoSnoozedStatus.SNOOZED_STATUS_UNSNOOZED
    }
