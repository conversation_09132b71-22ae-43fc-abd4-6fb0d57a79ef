<configuration>
    <if condition='property("LOGBACK_APPENDER_SDK").equalsIgnoreCase("ConsoleTextAppender")'>
        <then>
            <appender name="console-appender" class="ch.qos.logback.core.ConsoleAppender">
                <encoder>
                    <pattern>[%-5level] [%d{HH:mm:ss.SSS}] [%thread] %logger{36} - %msg%n</pattern>
                </encoder>
            </appender>
        </then>
        <else>
            <appender name="console-appender" class="ch.qos.logback.core.ConsoleAppender">
                <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
                    <providers>
                        <logLevel>
                            <fieldName>severity</fieldName>
                        </logLevel>
                        <timestamp>
                            <fieldName>time</fieldName>
                            <pattern>HH:mm:ss.SSS</pattern>
                        </timestamp>
                        <loggerName>
                            <fieldName>logger</fieldName>
                        </loggerName>
                        <threadName>
                            <fieldName>thread</fieldName>
                        </threadName>
                        <message>
                            <fieldName>message</fieldName>
                        </message>
                        <mdc>
                            <fieldName>mdc</fieldName>
                        </mdc>
                        <stackTrace>
                            <throwableConverter class="net.logstash.logback.stacktrace.ShortenedThrowableConverter">
                                <maxDepthPerThrowable>10</maxDepthPerThrowable>
                                <maxLength>2048</maxLength>
                                <shortenedClassNameLength>20</shortenedClassNameLength>
                                <exclude>sun\.reflect\..*\.invoke.*</exclude>
                                <exclude>net\.sf\.cglib\.proxy\.MethodProxy\.invoke</exclude>
                            </throwableConverter>
                            <fieldName>stackTrace</fieldName>
                        </stackTrace>
                    </providers>
                </encoder>
            </appender>
        </else>
    </if>

    <!-- In this section you can declare exceptions - loggers below will print logs on different level -->
    <logger name="com.zaxxer.hikari" level="${SDK_LOG_LEVEL_HIKARI:-INFO}"/>
    <logger name="io.netty" level="${SDK_LOG_LEVEL_NETTY:-INFO}"/>

    <!-- In this section proper appender and log level for all loggers (except the ones mentioned in exceptions section)
    are bound. You can easily make this section configurable by injecting values of environment variables and setting
    default values.
    For example: <root level="${LOGBACK_LOG_LEVEL_SDK:-INFO}"> -->
    <root level="${LOGBACK_LOG_LEVEL_SDK:-INFO}">
        <appender-ref ref="console-appender"/>
    </root>
</configuration>
